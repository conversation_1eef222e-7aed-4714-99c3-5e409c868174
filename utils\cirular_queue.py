import queue
import threading
import time


# TAG: 尚未使用 環狀 Queue 

class CircularQueue:
    def __init__(self, maxsize):
        self.data_queue = queue.Queue(maxsize=maxsize)

    def put(self, item):
        if self.data_queue.full():
            # 當隊列滿時，取出最早的數據並放入新的數據
            self.data_queue.get()
        self.data_queue.put(item)

    def get(self):
        return self.data_queue.get()

    def size(self):
        return self.data_queue.qsize()

    def empty(self):
        return self.data_queue.empty()

    def full(self):
        return self.data_queue.full()
    
    def clear(self):
        """清空隊列"""
        while not self.data_queue.empty():
            self.data_queue.get_nowait()  # 非阻塞地清空隊列

# class Producer(threading.Thread):
#     def __init__(self, circular_queue):
#         super().__init__()
#         self.circular_queue = circular_queue

#     def run(self):
#         for i in range(15):
#             print(f"Producing data {i}")
#             self.circular_queue.put(i)  # 嘗試放入數據
#             time.sleep(0.5)

# class Consumer(threading.Thread):
#     def __init__(self, circular_queue):
#         super().__init__()
#         self.circular_queue = circular_queue

#     def run(self):
#         while True:
#             if not self.circular_queue.empty():
#                 data = self.circular_queue.get()  # 從隊列取出數據
#                 print(f"Consuming data {data}")
#             time.sleep(1)

# # 設定隊列最大大小為 5
# circular_queue = CircularQueue(maxsize=5)

# # 創建並啟動生產者和消費者線程
# producer = Producer(circular_queue)
# consumer = Consumer(circular_queue)

# producer.start()
# consumer.start()

# producer.join()
