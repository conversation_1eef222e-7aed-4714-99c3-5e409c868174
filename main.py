# main.py
import sys
from PySide2.QtWidgets import QApplication
from app.models.model import Model
from app.views.view import View
from app.controllers.controller import Controller

from app.models.database_model import DatabaseModel

def main():
    app = QApplication(sys.argv)

    # 資料庫建置與確認
    machDatabase = DatabaseModel()

    # 軟體設定
    setting_items = machDatabase.getTableCount('machradar_setting')
    if setting_items < 1:
        machDatabase.add_item('machradar_setting',  communication='#BMW',  msra_file_path=r"D:\Record", language = '0', ip_start = 112,  ip_end = 117, server = 0)

    tool_items = machDatabase.getTableCount('tool_magazine')
    if tool_items < 1:
        machDatabase.add_item('tool_magazine',  
                              toolname='Josh182_TEST',  toolip='*************', toolmac="C0:49:EF:68:CE:48", sample_rate = 10000, 
                              tare_xv = 5.428,  tare_yv = 0.014, tare_zv = 0.037, tare_tv = 0.071, 
                              Linear_x = 1446, Linear_y = 1465, Linear_z = 31094, Linear_t = 1,
                              tare_gx = 0, tare_gy = 0, tare_gz = 0, 
                              auto_record_enabled = 0, auto_pre_record_seconds = 0, auto_record_seconds = 10, auto_max_record_count = 100,
                              auto_cf_enabled = 0, auto_fz_enabled = 0, auto_t_enabled = 0,
                              auto_cf_threshold = 0, auto_fz_threshold = 0, auto_t_threshold = 0,
                              Lc = 0.136, Hl = 1.1, Kl = 0.04,
                              Bx_COMP = 1446, By_COMP = 1465, Bz_COMP = 31094, Bt_COMP = 1
                              )

    # # ? 清空暫存資料 DB ****開發中暫時註解
    # machDatabase.deleteTable('tool_setting') 
    machDatabase.close_db()

    # 初始化模型、視圖和控制器
    model = Model()
    view = View()
    controller = Controller(model, view)

    # 顯示視圖
    view.show()

    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
