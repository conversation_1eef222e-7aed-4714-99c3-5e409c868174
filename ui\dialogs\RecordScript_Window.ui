<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RecordScript_Window</class>
 <widget class="QWidget" name="RecordScript_Window">
  <property name="enabled">
   <bool>true</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>578</width>
    <height>612</height>
   </rect>
  </property>
  <property name="font">
   <font>
    <family>Arial Black</family>
   </font>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="autoFillBackground">
   <bool>false</bool>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget#RecordScript_Window
{
background-color: #0C0C44;
border: 2px solid #5448B6;
}</string>
  </property>
  <property name="inputMethodHints">
   <set>Qt::ImhNone</set>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <item>
    <layout class="QHBoxLayout" name="title" stretch="1,100,1">
     <property name="rightMargin">
      <number>5</number>
     </property>
     <item>
      <widget class="QLabel" name="title_label">
       <property name="font">
        <font>
         <family>Arial Black</family>
         <pointsize>18</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">color: rgb(255, 255, 255);</string>
       </property>
       <property name="text">
        <string>錄製腳本</string>
       </property>
       <property name="textFormat">
        <enum>Qt::AutoText</enum>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_5">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="close_button">
       <property name="font">
        <font>
         <pointsize>20</pointsize>
        </font>
       </property>
       <property name="autoFillBackground">
        <bool>false</bool>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
    background: url(:/btn_close/btn_close_0.png) no-repeat right center;
    border: none;
}

QPushButton:hover {
    background: url(:/btn_close/btn_close_1.png) no-repeat right center;
}

QPushButton:pressed {
    background: url(:/btn_close/btn_close_2.png) no-repeat right center;
}</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="iconSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
       <property name="shortcut">
        <string/>
       </property>
       <property name="checkable">
        <bool>true</bool>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
       <property name="default">
        <bool>false</bool>
       </property>
       <property name="flat">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QVBoxLayout" name="mode">
     <item>
      <spacer name="verticalSpacer_4">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>535</width>
         <height>13</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <layout class="QHBoxLayout" name="default_mode">
       <item>
        <widget class="QRadioButton" name="default_mode_radio">
         <property name="font">
          <font>
           <family>Arial Black</family>
           <pointsize>11</pointsize>
           <weight>50</weight>
           <bold>false</bold>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QRadioButton{color: rgb(255, 255, 255);}


QRadioButton::indicator:unchecked{
image: url(:/btn_radio/btn_radio_s0_0.png);
}
QRadioButton::indicator:unchecked:hover {
image: url(:/btn_radio/btn_radio_s0_1.png);
}
QRadioButton::indicator:checked{
image: url(:/btn_radio/btn_radio_s1_0.png);
}
QRadioButton::indicator:checked:hover {
image: url(:/btn_radio/btn_radio_s1_1.png);
}
	</string>
         </property>
         <property name="text">
          <string>標準模式</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_10">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="verticalSpacer_2">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>535</width>
         <height>13</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <layout class="QHBoxLayout" name="machine_mode" stretch="0,0,0,2">
       <item>
        <widget class="QRadioButton" name="machine_mode_radio">
         <property name="font">
          <font>
           <family>Arial Black</family>
           <pointsize>11</pointsize>
           <weight>50</weight>
           <bold>false</bold>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QRadioButton{color: rgb(255, 255, 255);}


QRadioButton::indicator:unchecked{
image: url(:/btn_radio/btn_radio_s0_0.png);
}
QRadioButton::indicator:unchecked:hover {
image: url(:/btn_radio/btn_radio_s0_1.png);
}
QRadioButton::indicator:checked{
image: url(:/btn_radio/btn_radio_s1_0.png);
}
QRadioButton::indicator:checked:hover {
image: url(:/btn_radio/btn_radio_s1_1.png);
}
	</string>
         </property>
         <property name="text">
          <string>機器通訊模式</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_6">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QCheckBox" name="machine_mode_record_check">
         <property name="font">
          <font>
           <family>Arial Black</family>
           <pointsize>11</pointsize>
           <weight>50</weight>
           <bold>false</bold>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QCheckBox{color: rgb(255, 255, 255);}

QCheckBox::indicator:unchecked{
image: url(:/btn_check/btn_check_s0_0.png);
}
QCheckBox::indicator:unchecked:hover {
image: url(:/btn_check/btn_check_s0_1.png);
}
QCheckBox::indicator:checked{
image: url(:/btn_check/btn_check_s1_0.png);
}
QCheckBox::indicator:checked:hover {
image: url(:/btn_check/btn_check_s1_1.png);
}
	</string>
         </property>
         <property name="text">
          <string>錄製</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_8">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="verticalSpacer_3">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>535</width>
         <height>13</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <layout class="QHBoxLayout" name="time_mode" stretch="0,0,0,2">
       <item>
        <widget class="QRadioButton" name="time_mode_radio">
         <property name="font">
          <font>
           <family>Arial Black</family>
           <pointsize>11</pointsize>
           <weight>50</weight>
           <bold>false</bold>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QRadioButton{color: rgb(255, 255, 255);}


QRadioButton::indicator:unchecked{
image: url(:/btn_radio/btn_radio_s0_0.png);
}
QRadioButton::indicator:unchecked:hover {
image: url(:/btn_radio/btn_radio_s0_1.png);
}
QRadioButton::indicator:checked{
image: url(:/btn_radio/btn_radio_s1_0.png);
}
QRadioButton::indicator:checked:hover {
image: url(:/btn_radio/btn_radio_s1_1.png);
}
	</string>
         </property>
         <property name="text">
          <string>時間腳本模式</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_7">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QCheckBox" name="time_mode_record_check">
         <property name="font">
          <font>
           <family>Arial Black</family>
           <pointsize>11</pointsize>
           <weight>50</weight>
           <bold>false</bold>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QCheckBox{color: rgb(255, 255, 255);}

QCheckBox::indicator:unchecked{
image: url(:/btn_check/btn_check_s0_0.png);
}
QCheckBox::indicator:unchecked:hover {
image: url(:/btn_check/btn_check_s0_1.png);
}
QCheckBox::indicator:checked{
image: url(:/btn_check/btn_check_s1_0.png);
}
QCheckBox::indicator:checked:hover {
image: url(:/btn_check/btn_check_s1_1.png);
}
	</string>
         </property>
         <property name="text">
          <string>錄製</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_9">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="verticalSpacer_5">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>535</width>
         <height>13</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QGridLayout" name="RecordScript_ToolList_table">
     <item row="0" column="0">
      <widget class="QTableView" name="RecordScript_ToolList_Element_List_table">
       <property name="styleSheet">
        <string notr="true">background-color: rgb(0, 0, 0);
border:none</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <spacer name="horizontalSpacer_4">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QVBoxLayout" name="RecordScript_PlanList">
     <item>
      <layout class="QGridLayout" name="RecordScript_PlanList_label" columnstretch="2,3,2,2,2,4">
       <item row="1" column="0">
        <widget class="QLabel" name="Tool_Name">
         <property name="font">
          <font>
           <family>Arial Black</family>
           <pointsize>11</pointsize>
           <weight>50</weight>
           <bold>false</bold>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">color: rgb(255, 255, 255);</string>
         </property>
         <property name="text">
          <string>刀把名稱</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="1" column="2">
        <widget class="QLabel" name="time">
         <property name="font">
          <font>
           <family>Arial Black</family>
           <pointsize>11</pointsize>
           <weight>50</weight>
           <bold>false</bold>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">color: rgb(255, 255, 255);</string>
         </property>
         <property name="text">
          <string>期間</string>
         </property>
        </widget>
       </item>
       <item row="1" column="3">
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="1" column="4">
        <widget class="QLabel" name="Totaltime">
         <property name="font">
          <font>
           <family>Arial Black</family>
           <pointsize>11</pointsize>
           <weight>50</weight>
           <bold>false</bold>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">color: rgb(255, 255, 255);</string>
         </property>
         <property name="text">
          <string>總時間</string>
         </property>
        </widget>
       </item>
       <item row="1" column="5">
        <spacer name="horizontalSpacer_3">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QGridLayout" name="RecordScript_PlanList_table">
       <item row="0" column="0" rowspan="3">
        <widget class="QTableView" name="RecordScript_PlanList_Element_List_table">
         <property name="styleSheet">
          <string notr="true">background-color: rgb(0, 0, 0);
border:none</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QPushButton" name="reset_button">
         <property name="font">
          <font>
           <family>Arial Black</family>
           <pointsize>12</pointsize>
           <weight>50</weight>
           <bold>false</bold>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color: #0C0C44;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color: #0C0C44;
	border: 2px solid #7AFEC6;
    color: rgb(255, 255, 255);
	border-radius:5px;
	padding:2px;
}

QPushButton:pressed {
    background-color: gray;
	border: none;
}</string>
         </property>
         <property name="text">
          <string>重置</string>
         </property>
         <property name="checkable">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="2" column="1">
        <widget class="QPushButton" name="save_button">
         <property name="font">
          <font>
           <family>Arial Black</family>
           <pointsize>12</pointsize>
           <weight>50</weight>
           <bold>false</bold>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color:#5448B6;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color:#7AFEC6;
     color: rgb(255, 255, 255);
    border:none
}

QPushButton:pressed {
    background-color: gray;
	border:none
}</string>
         </property>
         <property name="text">
          <string>儲存</string>
         </property>
         <property name="checkable">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../system_image/system_image.qrc"/>
 </resources>
 <connections/>
</ui>
