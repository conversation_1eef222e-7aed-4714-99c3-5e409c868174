from PySide2.QtWidgets import QWidget,QApplication

def scale_widget(widget: QWidget, scale: float):
    # Scale font
    font = widget.font()
    font.setPointSizeF(font.pointSizeF() * scale)
    widget.setFont(font)

    # Scale size constraints
    if widget.minimumWidth() > 0:
        widget.setMinimumWidth(int(widget.minimumWidth() * scale))
    if widget.minimumHeight() > 0:
        widget.setMinimumHeight(int(widget.minimumHeight() * scale))
    if widget.maximumWidth() < 16777215:
        widget.setMaximumWidth(int(widget.maximumWidth() * scale))
    if widget.maximumHeight() < 16777215:
        widget.setMaximumHeight(int(widget.maximumHeight() * scale))

    # Recursively scale children
    for child in widget.findChildren(QWidget):
        scale_widget(child, scale)

    # Scale layout margins and spacings if applicable
    layout = widget.layout()
    if layout:
        left, top, right, bottom = layout.getContentsMargins()
        layout.setContentsMargins(
            int(left * scale), int(top * scale),
            int(right * scale), int(bottom * scale)
        )
        layout.setSpacing(int(layout.spacing() * scale))

def apply_scaling_to_window(window, base_width, base_height, show=True, parent=None):
    # Scale window size
    dpi_scaling(window, base_width, base_height)
    # Move window to center
    move_window(window, base_width, base_height, parent)

    if show:
        window.show()
        window.raise_()

    handle = window.windowHandle()
    if handle and not getattr(window, "_screen_changed_connected", False):
        print("[DEBUG] Connected screenChanged signal")
        handle.screenChanged.connect(
            lambda s: dpi_scaling(window, base_width, base_height)
        )
        window._screen_changed_connected = True

def dpi_scaling(window, base_width, base_height):
    screen = window.screen() or QApplication.primaryScreen()
    dpi = screen.logicalDotsPerInch() if screen else 96
    scale = dpi / 96.0

    # Resize with clamping to screen
    screen_geometry = screen.availableGeometry()
    new_width = min(int(base_width * scale), screen_geometry.width())
    new_height = min(int(base_height * scale), screen_geometry.height())
    window.setFixedSize(new_width, new_height)

def move_window(window, base_width, base_height, parent=None):
    screen = window.screen() or QApplication.primaryScreen()
    dpi = screen.logicalDotsPerInch() if screen else 96
    scale = dpi / 96.0
    screen_geometry = screen.availableGeometry()

    new_width = min(int(base_width * scale), screen_geometry.width())
    new_height = min(int(base_height * scale), screen_geometry.height())

    if parent:
        global_center = parent.mapToGlobal(parent.rect().center())
        center_x = global_center.x() - new_width // 2
        center_y = global_center.y() - new_height // 2
    else:
        center_x = screen_geometry.x() + (screen_geometry.width() - new_width) // 2
        center_y = screen_geometry.y() + (screen_geometry.height() - new_height) // 2

    window.move(center_x, center_y)

# BackUp
'''def apply_scaling_to_window_old(window, base_width, base_height):

    if not window.isVisible():
        print("[DEBUG] Window is not visible")
        window.show()  # Show it first so it gets associated with the correct screen
        window.raise_()

    screen = window.screen()
    if not screen:
        print("[DEBUG] Screen is not found")
        window.show()
        window.raise_()
        return

    dpi = screen.logicalDotsPerInch()
    scale = dpi / 96.0

    scaled_width = int(base_width * scale)
    scaled_height = int(base_height * scale)

    screen_geometry = screen.availableGeometry()
    # Clamp to screen
    new_width = min(scaled_width, screen_geometry.width())
    new_height = min(scaled_height, screen_geometry.height())

    # Center the window
    center_x = screen_geometry.x() + (screen_geometry.width() - new_width) // 2
    center_y = screen_geometry.y() + (screen_geometry.height() - new_height) // 2
    window.move(center_x, center_y)

    # Reapply scaling if moved to another screen
    handle = window.windowHandle()
    if handle:
        print("[DEBUG] Screen changed")
        handle.screenChanged.connect(lambda s: apply_scaling_to_window(window, base_width, base_height))

    window.show()
    window.raise_()

    print("[DEBUG] Window size:", window.size())'''