import math
from PySide2.QtCore import QObject, QThread, Signal, QTimer

import numpy as np

from app.models.co2_data import CO2Data
from app.models.filters import *
from app.models.tool_decoder_draft import ToolDecoder
from config import DEFAULT_CO2_DATA, DEFAULT_FILTER
from utils.auto_recording_manager import AutoRecordingManager
from utils.cirular_queue import CircularQueue
from utils.text_file_manager import TextFileManager
from . import logger

class DecoderWorker(QObject):  # 改成 QObject
    _HEX16_TO_FLOAT_DIVISOR: float = 6553.5
    decoded_data = Signal(object)  # 解析後的數據信號
    update_record_state = Signal(bool)  # 新增用來更新 record_state 的信號
    

    def __init__(self,holder_data):
        super().__init__()

        self.data = {}

        self.current_holder_data = holder_data
        self.tare_state = False
        self.paused = False # 新增「暫停」狀態

        # TDynamic Tare 狀態
        self.Dynamic_Tare = False
        self.Dynamic_Tare_Count=[0,False] # 動態tare 計數器歸零[計數器,紀錄(T/F)]
        self.Dynamic_Tare_BX = self.current_holder_data["tare_xv"]
        self.Dynamic_Tare_BY = self.current_holder_data["tare_yv"]
        self.Dynamic_Tare_BZ = self.current_holder_data["tare_zv"]
        self.Dynamic_Tare_BT = self.current_holder_data["tare_tv"]

        # 錄製狀態
        self.record_state  = False
        self.directory = r"C:\Record"  # 指定目錄名稱
        self.filename = "my_text_file.txt"
        self.rawdata_filename = "my_rawdata_file.txt"

        self.count_second=0
        # self.sample_N=160800
        # self.sample_index=160800//16  #資料每秒最多筆數

        self.SampleRate = int(self.current_holder_data["sample_rate"]) # 取資料
        self.SamplePoint1=math.ceil(self.SampleRate/12.5)  # 每一秒取5次
        self.sample_N=math.ceil(self.SamplePoint1 *1.005)
        self.sample_byte = int(self.sample_N * 16) 
        self.SamplePoint2=self.SamplePoint1//200  #每200筆資料1筆特殊
        self.sample_index=self.sample_N//16  #資料每秒最多筆數

        self.Tare_BX = self.current_holder_data["tare_xv"]
        self.Tare_BY = self.current_holder_data["tare_yv"]
        self.Tare_BZ = self.current_holder_data["tare_zv"]
        self.Tare_BT = self.current_holder_data["tare_tv"]

        # 剛性測試機 Lc = 0.136 m 客戶輸入伸出長 Lt = 0.166 m = 0.126 m + 0.04 m(伸出長)
        # TAG: 這裡先算出 N 換算係數
        self.parm_H_L = self.current_holder_data["Lc"] / (self.current_holder_data["Hl"] + self.current_holder_data["Kl"])
        self.parm_N_X =  self.current_holder_data["Linear_x"] * self.parm_H_L
        self.parm_N_Y =  self.current_holder_data["Linear_y"] * self.parm_H_L
        self.parm_N_Z =  self.current_holder_data["Linear_z"]
        self.parm_N_T =  self.current_holder_data["Linear_t"]

        logger.error(f"Tare_BX: {self.Tare_BX} Tare_BY: {self.Tare_BY} Tare_BZ:{self.Tare_BZ} Tare_BT:{self.Tare_BT}")
        logger.error(f"parm_N_X: {self.parm_N_X} parm_N_Y: {self.parm_N_Y} parm_N_Z:{self.parm_N_Z} parm_N_T:{self.parm_N_T}")

        # 初始化為0或False，避免未賦值時回傳None造成type error
        self.temp_RSSI = 0 # Wifi訊號
        self.temp_battery = 0 # 電池電壓
        self.Charging_Flag_temp = False # 充電狀態
        self.sleep_mode_temp = False # 睡眠模式
        self.MAC = None # MAC位址

        self.Tare_GX = self.current_holder_data["tare_gx"]
        self.Tare_GY = self.current_holder_data["tare_gy"]
        self.Tare_GZ = self.current_holder_data["tare_gz"]
        self.txt_gx = 0
        self.AutoTare = True

        self.running = False 
        self.data_queue = CircularQueue(maxsize=10)
        self.timer = QTimer()  # 新增 QTimer
        self.timer.timeout.connect(self.process_data)  # 定期處理數據
        self.update_record_state.connect(self.set_record_state) # 連接信號到槽
        # self.update_filter_state.connect(self.set_filter_state) # 連接信號到槽

        self.filter_data = DEFAULT_FILTER.copy()

        self.CO2_id = self.current_holder_data["CO2_id"]
        self.co2instance = CO2Data()
        self.CO2_data = dict(DEFAULT_CO2_DATA)

        self.display_decimal_places = 3  # 顯示小數點位數

        self.save_rawdatafile = False

        # 手動錄製檔案 manager
        self.record_decimal_places = 6
        self.msra_file_manager = None
        self.msra_rawdatafile_manager = None
        
        # Auto recording manager
        self.auto_recording_manager = AutoRecordingManager()
        self.auto_recording_manager.auto_record_triggered.connect(self._on_auto_record_triggered)
        self.auto_recording_manager.auto_record_stopped.connect(self._on_auto_record_stopped)
        
        # Configure auto recording with current holder data
        auto_recording_settings = {
            'auto_record_enabled': bool(self.current_holder_data.get('auto_record_enabled', 0)),
            'auto_pre_record_seconds': self.current_holder_data.get('auto_pre_record_seconds', 0),
            'auto_record_seconds': self.current_holder_data.get('auto_record_seconds', 10),
            'auto_max_record_count': self.current_holder_data.get('auto_max_record_count', 100),
            'sample_rate': self.SampleRate,
            'auto_cf_threshold': self.current_holder_data.get('auto_cf_threshold', 0.0),
            'auto_fz_threshold': self.current_holder_data.get('auto_fz_threshold', 0.0),
            'auto_t_threshold': self.current_holder_data.get('auto_t_threshold', 0.0),
            'auto_cf_enabled': bool(self.current_holder_data.get('auto_cf_enabled', 0)),
            'auto_fz_enabled': bool(self.current_holder_data.get('auto_fz_enabled', 0)),
            'auto_t_enabled': bool(self.current_holder_data.get('auto_t_enabled', 0))
        }
        self.auto_recording_manager.configure(auto_recording_settings)
        
        # Set auto recording file settings
        self.auto_recording_manager.set_file_settings(self.directory, "auto_record")

    def update_msra_file_data(self, directory, file_name):
        self.directory = directory
        self.filename = file_name
        self.rawdata_filename = "RawData_" + file_name

    def set_record_state(self, state):
        print(f"更新 record_state: {state}")
        self.record_state = state

        # 手動錄製檔案建立
        if self.record_state:
            self.create_msra_file()

        '''
        # 手動錄製檔案關閉
        if not self.record_state:
            self.close_msra_file()
        '''

    def create_msra_file(self):

        logger.error("create_msra_file")
        
        tool_info_data = f"!,{self.current_holder_data['sample_rate']},{round(self.current_holder_data['tare_xv'],self.record_decimal_places)},{round(self.current_holder_data['tare_yv'],self.record_decimal_places)},{round(self.current_holder_data['tare_zv'],self.record_decimal_places)},{round(self.current_holder_data['tare_tv'],self.record_decimal_places)},{self.current_holder_data['Linear_x']},{self.current_holder_data['Linear_y']},{self.current_holder_data['Linear_z']},-1,0,0,0,0,0,0,0,0,0,0,0,-1,{self.current_holder_data['Lc']},{self.current_holder_data['Hl']},{self.current_holder_data['Kl']},{self.current_holder_data['Bx_COMP']},{self.current_holder_data['By_COMP']},{self.current_holder_data['Bz_COMP']},{self.current_holder_data['Bt_COMP']}\n"
        logger.debug(f"tool_info_data: {tool_info_data}")

        logger.info(f"手動錄製檔案建立: {self.directory} {self.filename}")
        self.msra_file_manager = TextFileManager(self.directory)
        self.msra_file_manager.create_file(self.filename)
        self.msra_file_manager.add_text(self.filename, tool_info_data)

        if self.save_rawdatafile:
            logger.info(f"raw data create: {self.directory} {self.rawdata_filename}")
            self.msra_rawdatafile_manager = TextFileManager(self.directory)
            self.msra_rawdatafile_manager.create_file(self.rawdata_filename)
            self.msra_rawdatafile_manager.add_text(self.rawdata_filename, tool_info_data)

        logger.info("create_msra_file 完成")

    '''
    # TODO: 優化TextFileManager寫檔方式，在這裡關閉檔案
    def close_msra_file(self):
        logger.info(f"close_msra_file: {self.directory} {self.filename}")
        if self.msra_file_manager is not None:
            self.msra_file_manager.close_file()
            self.msra_file_manager = None

        if self.save_rawdatafile and self.msra_rawdatafile_manager is not None:
            logger.info(f"close_msra_rawdatafile: {self.directory} {self.rawdata_filename}")
            self.msra_rawdatafile_manager.close_file()
            self.msra_rawdatafile_manager = None
    '''
    
    def configure_auto_recording(self, settings):
        """Configure auto recording settings"""
        self.auto_recording_manager.configure(settings)
        self.auto_recording_manager.set_file_settings(self.directory, "auto_record")
        
    def set_auto_recording_file_settings(self, directory, filename):
        """Set file directory and filename for auto recording"""
        self.auto_recording_manager.set_file_settings(directory, filename)
        
    def _on_auto_record_triggered(self, trigger_reason):
        """Handle auto recording trigger event"""
        logger.info(f"Auto recording triggered: {trigger_reason}")
        # The auto recording manager handles its own file creation and writing
        
    def _on_auto_record_stopped(self):
        """Handle auto recording stop event"""
        logger.info("Auto recording stopped")
        
    def _process_auto_recording(self, sensor_data, data_line):
        """Process auto recording logic"""
        # Check for triggers
        if self.auto_recording_manager.check_triggers(sensor_data):
            # Trigger detected, auto recording manager will handle the rest
            pass
        
        # Process data point for pre-recording buffer and active recording
        self.auto_recording_manager.process_data_point(data_line)

    def add_data(self, data):
        """ 新增數據到解碼佇列 """
        # print(data)
        if self.running:
            self.data_queue.put(data)

    def start(self):
        """ 使用 QTimer 啟動非阻塞迴圈 """
        logger.info("DecoderWorker 開始運行")
        self.running = True
        self.paused = False  # 確保開始時不處於暫停狀態
        self.timer.start(5)  # 每 100ms 執行一次 process_data

    def pause(self):
        """ 暫停數據處理 """
        logger.info("DecoderWorker 暫停處理")
        self.Dynamic_Tare_Count=[0,False] # 動態tare 計數器歸零[計數器,紀錄(T/F)]
        self.paused = True

        # 暫停自動錄製
        if self.auto_recording_manager.is_recording:
            self.auto_recording_manager._stop_recording()
            self.auto_recording_manager.reset()

        # 暫停手動錄製
        if self.record_state:
            self.set_record_state(False)
        
        '''
        # 手動錄製檔案關閉
        if self.record_state:
            self.close_msra_file()
            # TODO: 更新UI的按鈕成沒有錄影的狀態
        '''

    def resume(self):
        """ 恢復數據處理 """
        logger.info("DecoderWorker 恢復運行")
        self.paused = False

    def process_data(self):
        """ 處理數據（取代 while self.running）"""
        
        if not self.running or self.paused:  # 檢查是否處於暫停狀態
            return
        
        if not self.data_queue.empty():
            raw = self.data_queue.get()
            try:
                if self.tare_state:
                    decoded = self.socket_tare_data(raw)
                    self.decoded_data.emit(decoded)
                else:
                    decoded = self.socket_decoder(raw)
                    self.decoded_data.emit(decoded)
                
            except Exception as e:
                logger.error(f"解碼失敗: {e}")

    def stop(self):
        """ 安全停止執行緒 """
        logger.info("DecoderWorker 正在停止...")
        self.data_queue.clear()
        self.co2instance.clear_accumulation() #換刀時，CO2累積值應歸零
        self.running = False
        self.paused = False  # 停止時確保不處於暫停狀態
        self.timer.stop()  # 停止 QTimer

    def _decode_low_freq_data(self, Collect_data: str, i: int, 
    MS_ADXL_X: np.ndarray, MS_ADXL_Y: np.ndarray, MS_ADXL_Z: np.ndarray, MS_Temperature: np.ndarray,
    offset: int = 32) -> tuple:
        """
        Returns: (RecordTextData, RawData, wifi_RSSI, is_charging, is_sleeping, MAC)
        """
        Data_ADXL_X = ToolDecoder.decode_hex16str_to_int(Collect_data[0+offset:4+offset])  
        Data_ADXL_Y = ToolDecoder.decode_hex16str_to_int(Collect_data[4+offset:8+offset])
        Data_ADXL_Z = ToolDecoder.decode_hex16str_to_int(Collect_data[8+offset:12+offset])
        data_temperature = ToolDecoder.decode_hex16str_to_int( ( Collect_data[14+offset:16+offset] +  Collect_data[12 +offset:14+offset] ) ) /128 #溫度，高低位元有錯位
        wifi_RSSI = ToolDecoder.decode_hex16str_to_int(Collect_data[16+offset:18+offset])

        txt_gx = round((Data_ADXL_X - self.Tare_GX), self.display_decimal_places)
        txt_gy = round((Data_ADXL_Y - self.Tare_GY), self.display_decimal_places)
        txt_gz = round((Data_ADXL_Z - self.Tare_GZ), self.display_decimal_places)
        index = i//201
        MS_ADXL_X[index] = txt_gx
        MS_ADXL_Y[index] = txt_gy
        MS_ADXL_Z[index] = txt_gz
        
        #TODO: 需要update DB的Tare_GX, Tare_GY, Tare_GZ
        if self.Tare_GX==0 :
            self.Tare_GX=np.mean( MS_ADXL_X )
            self.Tare_GY=np.mean( MS_ADXL_Y )
            self.Tare_GZ=np.mean( MS_ADXL_Z )

        if 125>data_temperature:  #DS620溫度感測範圍-55~125
            MS_Temperature[index] = data_temperature

        RecordTextData = "*," + str('{:.3f}'.format(txt_gx)) + ',' + \
            str('{:.3f}'.format(txt_gy)) + ',' + \
            str('{:.3f}'.format(txt_gz)) + ',' + \
            str('{:.2f}'.format(data_temperature)) + ',' \
            + str(wifi_RSSI) + '\n'
        RawData = "*," + str('{:.3f}'.format(txt_gx)) + ',' + \
            str('{:.3f}'.format(txt_gy)) + ',' + \
            str('{:.3f}'.format(txt_gz)) + ',' + \
            str('{:.2f}'.format(data_temperature)) + ',' \
            + str(wifi_RSSI) + '\n'

        is_charging, is_sleeping = ToolDecoder.parse_charging_and_sleeping_flags(
            ToolDecoder.decode_hex16str_to_int(Collect_data[19+offset:20+offset])
        )       
        MAC = Collect_data[20+offset:32+offset]

        return (RecordTextData, RawData, wifi_RSSI, is_charging, is_sleeping, MAC)

    def _decode_high_freq_data(self, Collect_data: str, i: int, 
    MS_BendingX: np.ndarray, MS_BendingY: np.ndarray, MS_BendingXY: np.ndarray, MS_Tension: np.ndarray, MS_Torsion: np.ndarray,
    dynamic_tare_bx: list, dynamic_tare_by: list, dynamic_tare_ten: list, dynamic_tare_tor: list,
    Append_index: int, offset: int = 32) -> tuple:
        Data_x= ToolDecoder.decode_hex16str_to_float(Collect_data[0+offset:4+offset])
        Data_y= ToolDecoder.decode_hex16str_to_float(Collect_data[4+offset:8+offset])
        Data_ten= ToolDecoder.decode_hex16str_to_float(Collect_data[8+offset:12+offset])
        Data_tor= ToolDecoder.decode_hex16str_to_float(Collect_data[12+offset:16+offset])
        Data_battery=round(ToolDecoder.decode_hex16str_to_float(Collect_data[16+offset:20+offset]), self.display_decimal_places)
        txt_ten = 0.0

        if self.Dynamic_Tare:
            Data_dynamic_x= int(Collect_data[0+offset:4+offset],16) /65535 *10
            Data_dynamic_y= int(Collect_data[4+offset:8+offset],16) /65535 *10
            Data_dynamic_ten= int(Collect_data[8+offset:12+offset],16) /65535 *10 
            Data_dynamic_tor= int(Collect_data[12+offset:16+offset],16) /65535 *10 
            dynamic_tare_bx.append( round( Data_dynamic_x,self.display_decimal_places) )
            dynamic_tare_by.append( round( Data_dynamic_y,self.display_decimal_places) )
            dynamic_tare_ten.append( round( Data_dynamic_ten,self.display_decimal_places) )
            dynamic_tare_tor.append( round( Data_dynamic_tor *7.33,self.display_decimal_places) )

        #Tare後的單位轉換資料儲存
        if self.Dynamic_Tare_Count[0] > 0:
            txt_bx = round((Data_x - self.Dynamic_Tare_BX) * self.parm_N_X,self.display_decimal_places) # Ftx = parm_N_X * Data_x(ΔV)
            txt_by = round((Data_y - self.Dynamic_Tare_BY)* self.parm_N_Y,self.display_decimal_places) # Ftx = parm_N_Y * Data_y(ΔV)
            txt_ten = round((Data_ten- self.Dynamic_Tare_BZ)* self.parm_N_Z,self.display_decimal_places) # Ftx = parm_N_Z * Data_z(ΔV)
            txt_tor = round((Data_tor- self.Dynamic_Tare_BT)* self.parm_N_T,self.display_decimal_places)
        else:
            txt_bx = round((Data_x - self.Tare_BX) * self.parm_N_X,self.display_decimal_places) # Ftx = parm_N_X * Data_x(ΔV)
            txt_by = round((Data_y - self.Tare_BY)* self.parm_N_Y,self.display_decimal_places) # Ftx = parm_N_Y * Data_y(ΔV)
            txt_ten = round((Data_ten- self.Tare_BZ)* self.parm_N_Z,self.display_decimal_places) # Ftx = parm_N_Z * Data_z(ΔV)
            txt_tor = round((Data_tor- self.Tare_BT)* self.parm_N_T,self.display_decimal_places)

        MS_BendingX[Append_index] = txt_bx   #index -198 是因為i從2開始算，且資料是201後面的200開始算
        MS_BendingY[Append_index] = txt_by
        MS_BendingXY[Append_index] = math.sqrt( (txt_bx*txt_bx) + (txt_by*txt_by) )
        MS_Tension[Append_index] = txt_ten
        MS_Torsion[Append_index] = txt_tor

        self.temp_battery = Data_battery

        #TODO: 存檔加入時間戳記
        RawData = str('{:.{}f}'.format(Data_x, 3)) + ', ' + \
            str('{:.{}f}'.format(Data_y, 3)) + ', ' + \
            str('{:.{}f}'.format(Data_ten, 3)) + ', ' + \
            str('{:.{}f}'.format(Data_tor, 3)) + ', ' + \
            str('{:.3f}'.format(Data_battery)) + '\n'
        RecordTextData = str('{:.{}f}'.format(txt_bx, 3)) + ', ' + \
            str('{:.{}f}'.format(txt_by, 3)) + ', ' + \
            str('{:.{}f}'.format(txt_ten, 3)) + ', ' + \
            str('{:.{}f}'.format(txt_tor, 3)) + ', ' + \
            str('{:.3f}'.format(Data_battery)) + '\n'

        if self.Dynamic_Tare:
            logger.debug(f"Dynamic_Tare: {self.Dynamic_Tare}")
            self.Dynamic_Tare = False
            self.Dynamic_Tare_Count[0]+=1

            if self.Dynamic_Tare_Count[0] > 0:
                logger.warning(f"Before: Tare_BX:{self.Tare_BX} Tare_BY:{self.Tare_BY} Tare_Tension:{self.Tare_BZ} Tare_Torsion:{self.Tare_BT}")
                self.Dynamic_Tare_BX=round(np.mean(dynamic_tare_bx),self.display_decimal_places)
                self.Dynamic_Tare_BY=round(np.mean(dynamic_tare_by),self.display_decimal_places)
                self.Dynamic_Tare_Tension=round(np.mean(dynamic_tare_ten),self.display_decimal_places)
                self.Dynamic_Tare_Torsion=round(np.mean(dynamic_tare_tor),self.display_decimal_places)
            else:
                logger.warning(f"Before: Tare_BX:{self.Dynamic_Tare_BX} Tare_BY:{self.Dynamic_Tare_BY} Tare_Tension:{self.Dynamic_Tare_Tension} Tare_Torsion:{self.Dynamic_Tare_Torsion}")
                self.Dynamic_Tare_BX=round(np.mean(dynamic_tare_bx),self.display_decimal_places)
                self.Dynamic_Tare_BY=round(np.mean(dynamic_tare_by),self.display_decimal_places)
                self.Dynamic_Tare_Tension=round(np.mean(dynamic_tare_ten),self.display_decimal_places)
                self.Dynamic_Tare_Torsion=round(np.mean(dynamic_tare_tor),self.display_decimal_places)
            
            logger.warning(f"After: Tare_BX:{self.Dynamic_Tare_BX} Tare_BY:{self.Dynamic_Tare_BY} Tare_Tension:{self.Dynamic_Tare_Tension} Tare_Torsion:{self.Dynamic_Tare_Torsion}")
        
        return (RecordTextData, RawData, txt_ten)

    def socket_decoder(self, hex_data: str) -> tuple:
        """
        解碼從socket接收到的十六進制數據。主要是將電壓換算為應力值
        Args: 十六進制字符串
        Returns: tuple: 包含彎曲力、張力、扭力、溫度、加速度計、WiFi、電池、CO2等數據
        """
      
        Append_index=0  #塞正常資料的index

        MS_BendingX = np.zeros(self.SamplePoint1)
        MS_BendingY = np.zeros(self.SamplePoint1)
        MS_BendingXY = np.zeros(self.SamplePoint1)
        MS_Tension = np.zeros(self.SamplePoint1)
        MS_Torsion = np.zeros(self.SamplePoint1)

        MS_Temperature = np.zeros(self.SamplePoint2)
        MS_ADXL_X = np.zeros(self.SamplePoint2)
        MS_ADXL_Y = np.zeros(self.SamplePoint2)
        MS_ADXL_Z = np.zeros(self.SamplePoint2)

        dynamic_tare_bx=[]
        dynamic_tare_by=[]
        dynamic_tare_ten=[]
        dynamic_tare_tor=[]

        Collect_data=hex_data
        RecordTextData=""
        RawData=""
        txt_ten = 0.0
        
        try:
            for i in range(self.sample_N):
                offset=i*32

                if Collect_data[0+offset:2+offset] in ["f1", "f2", "f3", "f4"]:
                    print(f"Collect_data: {Collect_data[0+offset:32+offset]}")
                    continue

                if i%201==0 :  #正常1+200 #第一筆放其他資訊的資料  
                    record_text, raw_text, wifi_RSSI, is_charging, is_sleeping, MAC = self._decode_low_freq_data(
                        Collect_data, i, 
                        MS_ADXL_X, MS_ADXL_Y, MS_ADXL_Z, MS_Temperature,
                        offset)
                    RecordTextData += record_text
                    RawData += raw_text
                    self.temp_RSSI = wifi_RSSI
                    self.Charging_Flag_temp = is_charging
                    self.sleep_mode_temp = is_sleeping
                    self.MAC = MAC
                    continue
 
                record_text, raw_text, txt_ten = self._decode_high_freq_data(
                    Collect_data, i, 
                    MS_BendingX, MS_BendingY, MS_BendingXY, MS_Tension, MS_Torsion,
                    dynamic_tare_bx, dynamic_tare_by, dynamic_tare_ten, dynamic_tare_tor,
                    Append_index, offset)
                RecordTextData += record_text
                RawData += raw_text
                Append_index+=1

        except Exception as e:
            logger.error(e)

        try:
            if self.record_state:
                self.msra_file_manager.add_text(self.filename, RecordTextData)
                # 根據 radioButton 狀態決定存檔內容
                if self.save_rawdatafile:
                    self.msra_rawdatafile_manager.add_text(self.rawdata_filename, RawData)
        except AttributeError:
            error_msg = f"檔案管理器未初始化，無法寫入檔案 {self.filename}"
            if self.save_rawdatafile:
                error_msg += f" 及 {self.rawdata_filename}"
            logger.error(error_msg)
        except FileNotFoundError as e:
            logger.error(f"檔案目錄不存在: {self.directory}, 錯誤: {e}")
        except PermissionError as e:
            logger.error(f"檔案寫入權限不足: {self.filename}, 錯誤: {e}")
        except Exception as e:
            logger.error(f"檔案儲存錯誤: {e}")

        # 套用濾波器
        if self.filter_data["filter_type"] != "Nofilter_radio":
            MS_BendingX, MS_BendingY, MS_BendingXY, MS_Tension, MS_Torsion = self._applyFilter(
                MS_BendingX, MS_BendingY, MS_BendingXY, MS_Tension, MS_Torsion
            )

        #計算CO2
        co2_status=self.CO2_data["init_status"] # 0:不計算, 1: 計算
        if co2_status == 1 :
            co2_g = round(
                self.co2instance.caculate_co2_g(txt_ten,self.SampleRate,self.CO2_data),
                self.display_decimal_places
                )
        else :
            co2_g = 0.0
        
        # Prepare sensor data for auto recording
        sensor_data = (MS_BendingX, MS_BendingY, MS_BendingXY, MS_Tension, MS_Torsion, 
                      MS_Temperature, MS_ADXL_X, MS_ADXL_Y, MS_ADXL_Z, self.temp_RSSI, 
                      self.Charging_Flag_temp, self.sleep_mode_temp, self.temp_battery, co2_g)
        
        # Process auto recording
        self._process_auto_recording(sensor_data, RecordTextData)
        
        return sensor_data  # 回傳已整理好的完整數據

    def socket_tare_data(self, hex_data):
        Append_index=0  #塞正常資料的index

        MS_BendingX = np.zeros(self.SamplePoint1)
        MS_BendingY = np.zeros(self.SamplePoint1)
        MS_BendingXY = np.zeros(self.SamplePoint1)
        MS_Tension = np.zeros(self.SamplePoint1)
        MS_Torsion = np.zeros(self.SamplePoint1)

        MS_Temperature = np.zeros(self.SamplePoint2)
        MS_ADXL_X = np.zeros(self.SamplePoint2)
        MS_ADXL_Y = np.zeros(self.SamplePoint2)
        MS_ADXL_Z = np.zeros(self.SamplePoint2)
        Collect_data=hex_data
        Data_x=0
        MAC = ""
        
        try:
            for i in range(self.sample_N):
                offset=i*32

                if Collect_data[0+offset:2+offset] in ["f1", "f2", "f3", "f4"]:
                    print(f"Collect_data: {Collect_data[0+offset:32+offset]}")
                    continue

                if i%201==0 :  #正常1+200 #第一筆放其他資訊的資料  
                    Data_ADXL_X, Data_ADXL_Y, Data_ADXL_Z, data_Temperature, wifi_RSSI, is_charging, is_sleeping, MAC = \
                        ToolDecoder.decode_low_freq_tare_reference(Collect_data, offset)

                    index = i//201
                    MS_ADXL_X[index] = round(Data_ADXL_X, self.display_decimal_places)
                    MS_ADXL_Y[index] = round(Data_ADXL_Y, self.display_decimal_places)
                    MS_ADXL_Z[index] = round(Data_ADXL_Z, self.display_decimal_places)
                    if 125 > data_Temperature:  #DS620溫度感測範圍-55~125
                        MS_Temperature[index] = data_Temperature
                    
                    self.temp_RSSI = wifi_RSSI
                    self.Charging_Flag_temp = is_charging
                    self.sleep_mode_temp = is_sleeping
                    self.MAC = MAC 
                    continue

                data_x, data_y, data_xy,data_ten, data_tor, data_battery = \
                    ToolDecoder.decode_high_freq_tare_reference(Collect_data, offset)
                MS_BendingX[Append_index] = data_x   
                MS_BendingY[Append_index] = data_y
                MS_BendingXY[Append_index] = data_xy
                MS_Tension[Append_index] = data_ten
                MS_Torsion[Append_index] = data_tor
                self.temp_battery = round(data_battery, self.display_decimal_places)
                Append_index+=1

                #電壓資料
                # print(f"Data_x: {Data_x}")
                # print(f"Data_y: {Data_y}")
                # print(f"Data_ten: {Data_ten}")
                # print(f"Data_tor: {Data_tor}")
                # print(f"Data_battery: {Data_tor}")
        except Exception as e:
            logger.error(f"發生錯誤：{e}")


        return (MS_BendingX, MS_BendingY, MS_BendingXY, MS_Tension, MS_Torsion, 
            MS_Temperature, MS_ADXL_X, MS_ADXL_Y, MS_ADXL_Z , 
            self.temp_RSSI, self.Charging_Flag_temp ,self.sleep_mode_temp, self.temp_battery)
    
    def __del__(self): 
        logger.info(f'DecoderWorker {self.current_holder_data["toolip"]} 已被銷毀')

    def _applyFilter(self, bending_x: np.ndarray, bending_y: np.ndarray, bending_xy: np.ndarray, tension: np.ndarray, torsion: np.ndarray) -> tuple:
        # 沒用濾波器則直接回傳
        if self.filter_data["filter_type"] == "Nofilter_radio":
            return bending_x, bending_y, bending_xy, tension, torsion
        
        # 根據濾波器類型進行處理
        if self.filter_data["filter_type"] == "Meanfilter_radio":
            k = self.filter_data["filter_values"]
            filter = MeanFilter(k)
        elif self.filter_data["filter_type"] == "Medianfilter_radio":
            k = self.filter_data["filter_values"]
            filter = MedianFilter(k)
        elif self.filter_data["filter_type"] == "Gaussianfilter_radio":
            k = self.filter_data["filter_values"]
            filter = GaussianFilter(k, 1)
        elif self.filter_data["filter_type"] == "MSfilter_radio":
            k = self.filter_data["filter_values"]
            filter = MSFilter(k, 0.003, 10, self.SampleRate, self.display_decimal_places)
        elif self.filter_data["filter_type"] == "Lowfilter_radio":
            cutoff_low = int(self.filter_data["Lowfilter_edit"])
            filter = ButterworthFilter(cutoff_low, self.SampleRate, "low", order=2)
        elif self.filter_data["filter_type"] == "Highfilter_radio":
            cutoff_high = int(self.filter_data["Highfilter_edit"])
            filter = ButterworthFilter(cutoff_high, self.SampleRate, "high")
        elif self.filter_data["filter_type"] == "SGfilter_radio":
            WL = int(self.filter_data["SGfilter_edit"])
            filter = SavitzkyGolayFilter(WL, 3)
        elif self.filter_data["filter_type"] == "MAfilter_radio":
            MV = int(self.filter_data["MAfilter_edit"])
            filter = MovingAverage(MV)
        else:
            logger.debug(f"undefined filter type: {self.filter_data['filter_type']}")
            return bending_x, bending_y, bending_xy, tension, torsion

        if self.filter_data["filter_type"] != "MAfilter_radio":
            filter.applyTo(bending_x)
            filter.applyTo(bending_y)
            bending_xy = np.sqrt(bending_x**2 + bending_y**2)
            filter.applyTo(tension)
            filter.applyTo(torsion)
        else:
            # 套用filter的順序與上面不同：先算bending_xy，再套用filter
            bending_xy = np.sqrt(bending_x**2 + bending_y**2)
            filter.applyTo(bending_x)
            filter.applyTo(bending_y)
            filter.applyTo(bending_xy)
            filter.applyTo(tension)
            filter.applyTo(torsion)

        return bending_x, bending_y, bending_xy, tension, torsion
