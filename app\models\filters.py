import numpy as np
from scipy import signal
import pandas as pd
from abc import ABC, abstractmethod


NdArrayFloat1D = np.ndarray  # 語意型別提示


class Filter(ABC):
    def applyTo(self, data: NdArrayFloat1D) -> None:
        if data is None or len(data) <= 1:
            return

        self._do_filter(data)

    @abstractmethod
    def _do_filter(self, data: NdArrayFloat1D) -> None:
        pass


class OddWindowSizeFilter(Filter):
    def __init__(self, window_size=3):
        super().__init__()
        self.window_size = window_size  # 會呼叫 setter

    @property
    def window_size(self):
        return self._window_size

    @window_size.setter
    def window_size(self, value):
        """自動調整成奇數"""
        if value % 2 == 0:
            value += 1
        self._window_size = value



class MovingAverage(Filter):
    def __init__(self, window_size: int = 3):   
        super().__init__()
        self.window_size = window_size

    def _do_filter(self, data: NdArrayFloat1D) -> None:
        Avg_Bending = pd.Series(data)
        result = Avg_Bending.rolling(window=self.window_size, min_periods=1).mean()
        data[:] = result.values


class MeanFilter(OddWindowSizeFilter):
    def _do_filter(self, data: NdArrayFloat1D) -> None:
        padding_size = self.window_size // 2
        data_padded = np.pad(data, pad_width=padding_size, mode='edge')  #copy with padding

        n = len(data)
        for i in range(n):
            data[i] = np.mean(data_padded[i : i + self.window_size])


class MedianFilter(OddWindowSizeFilter):
    def _do_filter(self, data: NdArrayFloat1D) -> None:
        padding_size = self.window_size // 2
        data_padded = np.pad(data, pad_width=padding_size, mode='edge')  #copy with padding

        n = len(data)
        for i in range(n):
            data[i] = np.median(data_padded[i : i + self.window_size])


class GaussianFilter(OddWindowSizeFilter):
    def __init__(self, window_size: int = 3, sigma: float = 1.0):
        self._sigma = sigma
        super().__init__(window_size)  #it will use _sigma to reset _kernel
    
    def _build_kernel(self) -> NdArrayFloat1D:
        half_size = self.window_size // 2
        filter_range = np.linspace(-half_size, half_size, self.window_size)
        kernel = [1 / (self.sigma * np.sqrt(2*np.pi)) * np.exp(-i**2 / (2*self.sigma**2)) for i in filter_range]
        kernel = np.array(kernel, dtype=np.float64)
        kernel /= kernel.sum()
        return kernel

    @OddWindowSizeFilter.window_size.setter
    def window_size(self, value):
        OddWindowSizeFilter.window_size.fset(self, value)
        self._kernel = self._build_kernel()

    @property
    def sigma(self):
        return self._sigma

    @sigma.setter
    def sigma(self, value):
        self._sigma = value
        self._kernel = self._build_kernel()

    def _do_filter(self, data: NdArrayFloat1D) -> None:
        padding_size = self.window_size // 2
        data_padded = np.pad(data, pad_width=padding_size, mode='edge')  #copy with padding
        data[:] = np.convolve(data_padded, self._kernel, mode='valid')


class MSFilter(Filter):
    """ Machsync濾波 """
    def __init__(self, filter_size: int = 3, diff_value: float = 0.003, result_value: float = 10, sample_rate: int = 10000, decimal_places: int = 3):      
        super().__init__()
        self.filter_size = filter_size
        self.diff_value = diff_value
        self.result_value = result_value
        self.sample_rate = sample_rate
        self.decimal_places = decimal_places

    def _do_filter(self, data: NdArrayFloat1D) -> None:
        result_list = data.copy()

        # 先進行中值濾波和均值濾波
        median_filter = MedianFilter(self.filter_size)
        median_filter.applyTo(result_list)
        mean_filter = MeanFilter(self.filter_size)
        mean_filter.applyTo(result_list)
        
        ms_sample_rate = self.sample_rate

        diff_x = []
        diff_time=[]
        index=0
        for i in range(0, len(result_list)-1):
            diff_x.append(abs(result_list[i] - result_list[i+1]))
            diff_time.append(index)
            index+=1

        tolerance_count = 0
        test_start = False
        threshold_value = max(diff_x) / 2
        result_value_tmp = round(max(data, key=abs) / self.result_value, self.decimal_places)

        for i in range(0, len(diff_x)):
            if (diff_x[i] > threshold_value and diff_x[i] > self.diff_value) or result_list[i] >= result_value_tmp:
                tolerance_count = ms_sample_rate * 0.05  # 保留0.05s資料
                result_list[i] = data[i]  # 保留原資料

                if test_start == False:  # 第一次超越起伏，保留前0.1s資料
                    temp_i = int(i - tolerance_count) if i - tolerance_count >= 0 else 0 #以防超越第0筆資料
                    result_list[temp_i:i] = data[temp_i:i]

                test_start = True

            elif tolerance_count > 0:
                tolerance_count -= 1
                result_list[i] = data[i]  # 保留原資料
            else:
                test_start = False
        
        data[:] = result_list


class ButterworthFilter(Filter):
    def __init__(self, cutoff_freq: float = 999,
    fs: int = 10000, btype: str = 'low', order: int = 4):     
        super().__init__()
        self.cutoff_freq = cutoff_freq
        self.fs = fs
        self.btype = btype
        self.order = order

    def _minRequiredLength(self, a, b) -> int:
        return 3 * max(len(b), len(a))  #this is the logic in scipy.signal.filtfilt

    def _do_filter(self, data: NdArrayFloat1D) -> None:
        nyquist_fs = self.fs / 2
        normalized_cutoff_freq = self.cutoff_freq / nyquist_fs
        b, a = signal.butter(self.order, normalized_cutoff_freq, self.btype)
        minRequiredLength = self._minRequiredLength(a, b)
        if len(data) < minRequiredLength:
            return
        data[:] = signal.filtfilt(b, a, data)


class SavitzkyGolayFilter(OddWindowSizeFilter):
    def __init__(self, window_size: int = 4, polyorder: int = 3):  
        super().__init__(window_size)
        self.polyorder = polyorder
    
    @OddWindowSizeFilter.window_size.setter
    def window_size(self, value):
        if hasattr(self, '_polyorder') and self.polyorder >= self.window_size:
            value += 1
        OddWindowSizeFilter.window_size.fset(self, value)

    @property
    def polyorder(self):
        return self._polyorder

    @polyorder.setter
    def polyorder(self, value):
        self._polyorder = min(value, self.window_size - 1) #polyorder must be less than window_size

    def _do_filter(self, data: NdArrayFloat1D) -> None:
        minRequiredLength = self.window_size
        if len(data) < minRequiredLength:
            return
        data[:] = signal.savgol_filter(data, self.window_size, self._polyorder)
