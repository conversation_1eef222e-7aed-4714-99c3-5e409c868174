# 簡介
本軟體專案是 MachRadar 軟體的 MVC 重構版本。
舊版請見 https://github.com/machsync/pro_RealTime

🚨🚨 刀把搜尋功能 🚨🚨

目前綁在 [研華AP] 上 ，如果要換掉研華AP 刀把搜尋需要修改

🚨🚨 刀把搜尋功能 🚨🚨

# 如何以 .py 檔案執行

## 1. 配置執行環境
執行環境內先安裝好以下套件：
- npm
- python 3.8.10
- PySide2
- [Qt Designer](https://build-system.fman.io/qt-designer-download)
- 其他套件請查看 requirements.txt 檔案


## 2. 配置檔案
下載專案，然後在專案資料夾下建立 .env 檔案，填入以下內容：
``` bash
LOG_TO_FILE=True # True / False 日誌開關 
NETWORK_DEVICE_URL=https://192.168.0.1 # AP後台網址 
NETWORK_DEVICE_USERNAME=admin
NETWORK_DEVICE_PASSWORD=admin
``` 

接著在專案資料夾下建立一個新資料夾，取名為 db，
然後從 [建置與啟動 mvc_pro_RealTime 專案 | 手動配置DB](https://machsynchank.atlassian.net/wiki/spaces/R/pages/15073312/mvc_pro_RealTime#2.3-%E6%89%8B%E5%8B%95%E9%85%8D%E7%BD%AEDB) 找到檔案 machradar.mcdb，將其放入剛剛新建的資料夾。


## 3. 運行
執行
``` bash
npm run dev
```



# 如何打包成 exe
## QT-pyside2  UI To Python
``` bash
pyside2-uic qt_designer.ui > qt_designer.py # 根據 UI 名稱做修改
# OR
npm run build-ui # 根據 UI 名稱修改 package build-ui 單一打包
# OR
pyside2-rcc system_image.qrc -o system_image_rc.py # 靜態圖片打包方式

```

## Build with Pyinstaller To EXE
``` bash
# 參數 -w 關閉 command line 視窗 通常是 debug 會拿掉 測試
# 參數 -F One File
# 參數 -i .\system_image\machradarpro.ico 加入icon  
# 參數 --version-file=version_info.version 加入版本

# Test 測試 debug 用
pyinstaller -F main.py
# build
pyinstaller -w -F -i .\system_image\machradarpro.ico --version-file=version_info.version main.py

```

# 其他說明
## Format Python
``` bash
# Format Python
pip install black
black <file-you-want-to-format>.py # format單一檔案
black <folder-you-want-to-format>  # format整個目錄
black -check <file-you-want-to-format>.py # 檢查是否需要修改；不會直接修改
``` 



## 檔案架構
``` bash
project_root/
├── main.py                 # 主程式入口，啟動應用程式
├── config.py               # 配置檔案，用於存放設定參數
├── requirements.txt        # 依賴需求清單
├── README.md               # 專案說明文件
├── resources/              # 靜態資源文件夾（圖片、字型、樣式表等）
│   ├── images/
│   ├── fonts/
│   └── styles.qss          # QSS樣式表
├── ui/                     # 存放 UI 文件
│   ├── main_window.ui      # 使用 Qt Designer 設計的 UI 文件
│   └── dialogs/            # 存放各種對話框的 UI 文件
├── app/                    # 主應用程式模組
│   ├── __init__.py
│   ├── main_window.py      # 主視窗的邏輯
│   ├── dialogs/            # 各種對話框的邏輯
│   │   └── custom_dialog.py
│   ├── controllers/        # 控制器，用於處理業務邏輯
│   ├── models/             # 資料模型，儲存應用的數據結構和處理
│   └── views/              # 視圖模組，專注於 UI 與模型的互動
└── utils/                  # 實用工具函式
    ├── __init__.py
    ├── helpers.py          # 常用函式
    └── database.py         # 資料庫連線與操作
```



## Git writting style 小抄
### branch
- master是穩定版本所在的分支，不可擅動
- develop是開發中版本所在的分支，一般從這邊開新分支，新分支也合併回這裡
#### 新增與命名規則
(待補)
#### branch的合併
(待補)

### commit
一則commit必須有title，有時還會有content與footer。
commit title 推薦遵循 <類型>: <簡短描述> 的格式，類型從以下列表選擇：
- feat: 新增/修改功能 (feature)。
- fix: 修補 bug (bug fix)。
- docs: 文件 (documentation)。
- style: 格式 (不影響程式碼運行的變動 white-space, formatting, missing semi colons, etc)。
- refactor: 重構 (既不是新增功能，也不是修補 bug 的程式碼變動)。
- perf: 改善效能 (A code change that improves performance)。
- test: 增加測試 (when adding missing tests)。
- chore: 建構程序或輔助工具的變動 (maintain)。
- revert: 撤銷回覆先前的 commit 例如：revert: type(scope): subject (回覆版本：xxxx)。

如果該commmit是對應某個issue，請寫上issue編號。



## 版號規則小抄
v a.b.ccc.d  (ex. v1.12.902.121)
- a: 主版號
- b: 次版號
- ccc: 第一碼為備註層級，後兩碼為修訂號
    - 備註層級代碼：
    - 6 = Pre-Alpha
    - 7 = Open Beta
    - 8 = Release Candidate
    - 9 = Stable
- d: 產品編碼
