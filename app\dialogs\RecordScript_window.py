# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'RecordScript_Window.ui'
##
## Created by: Qt User Interface Compiler version 5.15.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *

import system_image.system_image_rc

class Ui_RecordScript_Window(object):
    def setupUi(self, RecordScript_Window):
        if not RecordScript_Window.objectName():
            RecordScript_Window.setObjectName(u"RecordScript_Window")
        RecordScript_Window.setEnabled(True)
        RecordScript_Window.resize(578, 612)
        font = QFont()
        font.setFamily(u"Arial Black")
        RecordScript_Window.setFont(font)
        RecordScript_Window.setAutoFillBackground(False)
        RecordScript_Window.setStyleSheet(u"QWidget#RecordScript_Window\n"
"{\n"
"background-color: #0C0C44;\n"
"border: 2px solid #5448B6;\n"
"}")
        RecordScript_Window.setInputMethodHints(Qt.ImhNone)
        self.verticalLayout_3 = QVBoxLayout(RecordScript_Window)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.title = QHBoxLayout()
        self.title.setObjectName(u"title")
        self.title.setContentsMargins(-1, -1, 5, -1)
        self.title_label = QLabel(RecordScript_Window)
        self.title_label.setObjectName(u"title_label")
        font1 = QFont()
        font1.setFamily(u"Arial Black")
        font1.setPointSize(18)
        font1.setBold(True)
        font1.setWeight(75)
        self.title_label.setFont(font1)
        self.title_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.title_label.setTextFormat(Qt.AutoText)

        self.title.addWidget(self.title_label)

        self.horizontalSpacer_5 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.title.addItem(self.horizontalSpacer_5)

        self.close_button = QPushButton(RecordScript_Window)
        self.close_button.setObjectName(u"close_button")
        font2 = QFont()
        font2.setPointSize(20)
        self.close_button.setFont(font2)
        self.close_button.setAutoFillBackground(False)
        self.close_button.setStyleSheet(u"QPushButton {\n"
"    background: url(:/btn_close/btn_close_0.png) no-repeat right center;\n"
"    border: none;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background: url(:/btn_close/btn_close_1.png) no-repeat right center;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: url(:/btn_close/btn_close_2.png) no-repeat right center;\n"
"}")
        self.close_button.setIconSize(QSize(30, 30))
        self.close_button.setCheckable(True)
        self.close_button.setAutoDefault(False)
        self.close_button.setFlat(False)

        self.title.addWidget(self.close_button)

        self.title.setStretch(0, 1)
        self.title.setStretch(1, 100)
        self.title.setStretch(2, 1)

        self.verticalLayout_3.addLayout(self.title)

        self.mode = QVBoxLayout()
        self.mode.setObjectName(u"mode")
        self.verticalSpacer_4 = QSpacerItem(535, 13, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.mode.addItem(self.verticalSpacer_4)

        self.default_mode = QHBoxLayout()
        self.default_mode.setObjectName(u"default_mode")
        self.default_mode_radio = QRadioButton(RecordScript_Window)
        self.default_mode_radio.setObjectName(u"default_mode_radio")
        font3 = QFont()
        font3.setFamily(u"Arial Black")
        font3.setPointSize(11)
        font3.setBold(False)
        font3.setWeight(50)
        self.default_mode_radio.setFont(font3)
        self.default_mode_radio.setStyleSheet(u"QRadioButton{color: rgb(255, 255, 255);}\n"
"\n"
"\n"
"QRadioButton::indicator:unchecked{\n"
"image: url(:/btn_radio/btn_radio_s0_0.png);\n"
"}\n"
"QRadioButton::indicator:unchecked:hover {\n"
"image: url(:/btn_radio/btn_radio_s0_1.png);\n"
"}\n"
"QRadioButton::indicator:checked{\n"
"image: url(:/btn_radio/btn_radio_s1_0.png);\n"
"}\n"
"QRadioButton::indicator:checked:hover {\n"
"image: url(:/btn_radio/btn_radio_s1_1.png);\n"
"}\n"
"	")

        self.default_mode.addWidget(self.default_mode_radio)

        self.horizontalSpacer_10 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.default_mode.addItem(self.horizontalSpacer_10)


        self.mode.addLayout(self.default_mode)

        self.verticalSpacer_2 = QSpacerItem(535, 13, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.mode.addItem(self.verticalSpacer_2)

        self.machine_mode = QHBoxLayout()
        self.machine_mode.setObjectName(u"machine_mode")
        self.machine_mode_radio = QRadioButton(RecordScript_Window)
        self.machine_mode_radio.setObjectName(u"machine_mode_radio")
        self.machine_mode_radio.setFont(font3)
        self.machine_mode_radio.setStyleSheet(u"QRadioButton{color: rgb(255, 255, 255);}\n"
"\n"
"\n"
"QRadioButton::indicator:unchecked{\n"
"image: url(:/btn_radio/btn_radio_s0_0.png);\n"
"}\n"
"QRadioButton::indicator:unchecked:hover {\n"
"image: url(:/btn_radio/btn_radio_s0_1.png);\n"
"}\n"
"QRadioButton::indicator:checked{\n"
"image: url(:/btn_radio/btn_radio_s1_0.png);\n"
"}\n"
"QRadioButton::indicator:checked:hover {\n"
"image: url(:/btn_radio/btn_radio_s1_1.png);\n"
"}\n"
"	")

        self.machine_mode.addWidget(self.machine_mode_radio)

        self.horizontalSpacer_6 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.machine_mode.addItem(self.horizontalSpacer_6)

        self.machine_mode_record_check = QCheckBox(RecordScript_Window)
        self.machine_mode_record_check.setObjectName(u"machine_mode_record_check")
        self.machine_mode_record_check.setFont(font3)
        self.machine_mode_record_check.setStyleSheet(u"QCheckBox{color: rgb(255, 255, 255);}\n"
"\n"
"QCheckBox::indicator:unchecked{\n"
"image: url(:/btn_check/btn_check_s0_0.png);\n"
"}\n"
"QCheckBox::indicator:unchecked:hover {\n"
"image: url(:/btn_check/btn_check_s0_1.png);\n"
"}\n"
"QCheckBox::indicator:checked{\n"
"image: url(:/btn_check/btn_check_s1_0.png);\n"
"}\n"
"QCheckBox::indicator:checked:hover {\n"
"image: url(:/btn_check/btn_check_s1_1.png);\n"
"}\n"
"	")

        self.machine_mode.addWidget(self.machine_mode_record_check)

        self.horizontalSpacer_8 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.machine_mode.addItem(self.horizontalSpacer_8)

        self.machine_mode.setStretch(3, 2)

        self.mode.addLayout(self.machine_mode)

        self.verticalSpacer_3 = QSpacerItem(535, 13, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.mode.addItem(self.verticalSpacer_3)

        self.time_mode = QHBoxLayout()
        self.time_mode.setObjectName(u"time_mode")
        self.time_mode_radio = QRadioButton(RecordScript_Window)
        self.time_mode_radio.setObjectName(u"time_mode_radio")
        self.time_mode_radio.setFont(font3)
        self.time_mode_radio.setStyleSheet(u"QRadioButton{color: rgb(255, 255, 255);}\n"
"\n"
"\n"
"QRadioButton::indicator:unchecked{\n"
"image: url(:/btn_radio/btn_radio_s0_0.png);\n"
"}\n"
"QRadioButton::indicator:unchecked:hover {\n"
"image: url(:/btn_radio/btn_radio_s0_1.png);\n"
"}\n"
"QRadioButton::indicator:checked{\n"
"image: url(:/btn_radio/btn_radio_s1_0.png);\n"
"}\n"
"QRadioButton::indicator:checked:hover {\n"
"image: url(:/btn_radio/btn_radio_s1_1.png);\n"
"}\n"
"	")

        self.time_mode.addWidget(self.time_mode_radio)

        self.horizontalSpacer_7 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.time_mode.addItem(self.horizontalSpacer_7)

        self.time_mode_record_check = QCheckBox(RecordScript_Window)
        self.time_mode_record_check.setObjectName(u"time_mode_record_check")
        self.time_mode_record_check.setFont(font3)
        self.time_mode_record_check.setStyleSheet(u"QCheckBox{color: rgb(255, 255, 255);}\n"
"\n"
"QCheckBox::indicator:unchecked{\n"
"image: url(:/btn_check/btn_check_s0_0.png);\n"
"}\n"
"QCheckBox::indicator:unchecked:hover {\n"
"image: url(:/btn_check/btn_check_s0_1.png);\n"
"}\n"
"QCheckBox::indicator:checked{\n"
"image: url(:/btn_check/btn_check_s1_0.png);\n"
"}\n"
"QCheckBox::indicator:checked:hover {\n"
"image: url(:/btn_check/btn_check_s1_1.png);\n"
"}\n"
"	")

        self.time_mode.addWidget(self.time_mode_record_check)

        self.horizontalSpacer_9 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.time_mode.addItem(self.horizontalSpacer_9)

        self.time_mode.setStretch(3, 2)

        self.mode.addLayout(self.time_mode)

        self.verticalSpacer_5 = QSpacerItem(535, 13, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.mode.addItem(self.verticalSpacer_5)


        self.verticalLayout_3.addLayout(self.mode)

        self.RecordScript_ToolList_table = QGridLayout()
        self.RecordScript_ToolList_table.setObjectName(u"RecordScript_ToolList_table")
        self.RecordScript_ToolList_Element_List_table = QTableView(RecordScript_Window)
        self.RecordScript_ToolList_Element_List_table.setObjectName(u"RecordScript_ToolList_Element_List_table")
        self.RecordScript_ToolList_Element_List_table.setStyleSheet(u"background-color: rgb(0, 0, 0);\n"
"border:none")

        self.RecordScript_ToolList_table.addWidget(self.RecordScript_ToolList_Element_List_table, 0, 0, 1, 1)

        self.horizontalSpacer_4 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.RecordScript_ToolList_table.addItem(self.horizontalSpacer_4, 0, 1, 1, 1)


        self.verticalLayout_3.addLayout(self.RecordScript_ToolList_table)

        self.RecordScript_PlanList = QVBoxLayout()
        self.RecordScript_PlanList.setObjectName(u"RecordScript_PlanList")
        self.RecordScript_PlanList_label = QGridLayout()
        self.RecordScript_PlanList_label.setObjectName(u"RecordScript_PlanList_label")
        self.Tool_Name = QLabel(RecordScript_Window)
        self.Tool_Name.setObjectName(u"Tool_Name")
        self.Tool_Name.setFont(font3)
        self.Tool_Name.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.RecordScript_PlanList_label.addWidget(self.Tool_Name, 1, 0, 1, 1)

        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.RecordScript_PlanList_label.addItem(self.horizontalSpacer, 1, 1, 1, 1)

        self.time = QLabel(RecordScript_Window)
        self.time.setObjectName(u"time")
        self.time.setFont(font3)
        self.time.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.RecordScript_PlanList_label.addWidget(self.time, 1, 2, 1, 1)

        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.RecordScript_PlanList_label.addItem(self.horizontalSpacer_2, 1, 3, 1, 1)

        self.Totaltime = QLabel(RecordScript_Window)
        self.Totaltime.setObjectName(u"Totaltime")
        self.Totaltime.setFont(font3)
        self.Totaltime.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.RecordScript_PlanList_label.addWidget(self.Totaltime, 1, 4, 1, 1)

        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.RecordScript_PlanList_label.addItem(self.horizontalSpacer_3, 1, 5, 1, 1)

        self.RecordScript_PlanList_label.setColumnStretch(0, 2)
        self.RecordScript_PlanList_label.setColumnStretch(1, 3)
        self.RecordScript_PlanList_label.setColumnStretch(2, 2)
        self.RecordScript_PlanList_label.setColumnStretch(3, 2)
        self.RecordScript_PlanList_label.setColumnStretch(4, 2)
        self.RecordScript_PlanList_label.setColumnStretch(5, 4)

        self.RecordScript_PlanList.addLayout(self.RecordScript_PlanList_label)

        self.RecordScript_PlanList_table = QGridLayout()
        self.RecordScript_PlanList_table.setObjectName(u"RecordScript_PlanList_table")
        self.RecordScript_PlanList_Element_List_table = QTableView(RecordScript_Window)
        self.RecordScript_PlanList_Element_List_table.setObjectName(u"RecordScript_PlanList_Element_List_table")
        self.RecordScript_PlanList_Element_List_table.setStyleSheet(u"background-color: rgb(0, 0, 0);\n"
"border:none")

        self.RecordScript_PlanList_table.addWidget(self.RecordScript_PlanList_Element_List_table, 0, 0, 3, 1)

        self.reset_button = QPushButton(RecordScript_Window)
        self.reset_button.setObjectName(u"reset_button")
        font4 = QFont()
        font4.setFamily(u"Arial Black")
        font4.setPointSize(12)
        font4.setBold(False)
        font4.setWeight(50)
        self.reset_button.setFont(font4)
        self.reset_button.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color: #0C0C44;\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #0C0C44;\n"
"	border: 2px solid #7AFEC6;\n"
"    color: rgb(255, 255, 255);\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border: none;\n"
"}")
        self.reset_button.setCheckable(True)

        self.RecordScript_PlanList_table.addWidget(self.reset_button, 0, 1, 1, 1)

        self.verticalSpacer = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.RecordScript_PlanList_table.addItem(self.verticalSpacer, 1, 1, 1, 1)

        self.save_button = QPushButton(RecordScript_Window)
        self.save_button.setObjectName(u"save_button")
        self.save_button.setFont(font4)
        self.save_button.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color:#5448B6;\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color:#7AFEC6;\n"
"     color: rgb(255, 255, 255);\n"
"    border:none\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border:none\n"
"}")
        self.save_button.setCheckable(True)

        self.RecordScript_PlanList_table.addWidget(self.save_button, 2, 1, 1, 1)


        self.RecordScript_PlanList.addLayout(self.RecordScript_PlanList_table)


        self.verticalLayout_3.addLayout(self.RecordScript_PlanList)


        self.retranslateUi(RecordScript_Window)

        self.close_button.setDefault(False)


        QMetaObject.connectSlotsByName(RecordScript_Window)
    # setupUi

    def retranslateUi(self, RecordScript_Window):
        RecordScript_Window.setWindowTitle(QCoreApplication.translate("RecordScript_Window", u"Form", None))
        self.title_label.setText(QCoreApplication.translate("RecordScript_Window", u"\u9304\u88fd\u8173\u672c", None))
        self.close_button.setText("")
#if QT_CONFIG(shortcut)
        self.close_button.setShortcut("")
#endif // QT_CONFIG(shortcut)
        self.default_mode_radio.setText(QCoreApplication.translate("RecordScript_Window", u"\u6a19\u6e96\u6a21\u5f0f", None))
        self.machine_mode_radio.setText(QCoreApplication.translate("RecordScript_Window", u"\u6a5f\u5668\u901a\u8a0a\u6a21\u5f0f", None))
        self.machine_mode_record_check.setText(QCoreApplication.translate("RecordScript_Window", u"\u9304\u88fd", None))
        self.time_mode_radio.setText(QCoreApplication.translate("RecordScript_Window", u"\u6642\u9593\u8173\u672c\u6a21\u5f0f", None))
        self.time_mode_record_check.setText(QCoreApplication.translate("RecordScript_Window", u"\u9304\u88fd", None))
        self.Tool_Name.setText(QCoreApplication.translate("RecordScript_Window", u"\u5200\u628a\u540d\u7a31", None))
        self.time.setText(QCoreApplication.translate("RecordScript_Window", u"\u671f\u9593", None))
        self.Totaltime.setText(QCoreApplication.translate("RecordScript_Window", u"\u7e3d\u6642\u9593", None))
        self.reset_button.setText(QCoreApplication.translate("RecordScript_Window", u"\u91cd\u7f6e", None))
        self.save_button.setText(QCoreApplication.translate("RecordScript_Window", u"\u5132\u5b58", None))
    # retranslateUi

