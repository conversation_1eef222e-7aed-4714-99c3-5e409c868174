import sys
import numpy as np
import pyqtgraph.opengl as gl
from PySide2.QtWidgets import QApplication, QOpenGLWidget
from PySide2.QtGui import QVector3D
from PySide2.QtCore import QTimer
from OpenGL.GL import *
from OpenGL.GLUT import *
from OpenGL.GLUT import (GLUT_BITMAP_HELVETICA_10, GLUT_BITMAP_HELVETICA_12,
                         GLUT_BITMAP_HELVETICA_18, GLUT_BITMAP_TIMES_ROMAN_24)
from . import logger  # 從同一個包導入 logger
import math
from pyqtgraph import Vector
import warnings
warnings.filterwarnings("ignore", category=RuntimeWarning) # 暫時忽略經常出現的警告

class GLTextItem(gl.GLGraphicsItem.GLGraphicsItem):
    """ 3D 文字標籤 (XYZ 軸刻度數字) """

    def __init__(self, text, position, color=(1, 1, 1, 1), size=14):
        super().__init__()
        self.text = text
        self.position = position
        self.color = color
        self.size = size
        glutInit()

    def paint(self):
        """ 繪製數字標籤 """
        glColor4f(*self.color)
        glRasterPos3f(self.position.x(), self.position.y(), self.position.z())
        # 根據大小選擇不同的字體
        if self.size <= 10:
            font = GLUT_BITMAP_HELVETICA_10
        elif self.size <= 12:
            font = GLUT_BITMAP_HELVETICA_12
        elif self.size <= 18:
            font = GLUT_BITMAP_HELVETICA_18
        else:
            font = GLUT_BITMAP_TIMES_ROMAN_24

        glutBitmapString(font, self.text.encode("utf-8"))

class Animation_3D(QOpenGLWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.scatter = None  # 先初始化 scatter 變數
        self.line = None  # 先初始化 line 變數
        self.scatter_adxl = None  # 先初始化 ADXL scatter 變數
        self.SamplePoint2 = 0
        self.show_target = False  # 新增變數控制靶心顯示
        self.CF_maximum = 10
        self.target_scale = 1.0  # 新增靶心縮放比例
        self.show_ring = False  # 新增變數控制 Ring 顯示
        self.ring_mesh = None  # Ring 網格物件
        self.tick_label_size = 12  # 刻度數字大小
        self.axis_label_size = 18  # 軸名稱大小
        self.show_axis_labels = True  # 是否顯示軸名稱
        
        # 建立 OpenGL 視圖
        self.view = gl.GLViewWidget(self)
       
        self.view.setParent(self)
        self.view.opts['fov'] = 1  # 視角對應 gluPerspective(1, 1, ...)
        # TAG: 還原視角重點功能 distance:調整視角遠近, elevation:顯示角度, azimuth:調整視角旋轉
        # self.view.setCameraPosition(distance=2600, elevation=90, azimuth=180)
        self.view.setCameraPosition(distance=1500, elevation=90, azimuth=270)

        # 添加 XYZ 軸線 & 刻度
        # self.add_axis()
        # self.add_grid()
        # self.add_labels()

        # other
        # self.update_grid(100, 2) # 將網格範圍設為 100，間距設為 2
        # self.update_axis(50)  # 調整 XYZ 軸的長度到 50
        # self.update_labels(10, 50)  # 設定 XYZ 刻度間距為 10，範圍為 -50 ~ 50
        # self.update_scene(axis_length=20, grid_size=20, grid_spacing=1, tick_spacing=2)
        # TAG: 調整網格大小、間距、軸長度、刻度間距
        # self.update_scene(axis_length=20, grid_size=40, grid_spacing=2, tick_spacing=2)
        self.update_scene(axis_length=10, grid_size=20, grid_spacing=1, tick_spacing=2)

        self.SampleRate = 10000 # 取資料
        self.SamplePoint1=math.ceil(self.SampleRate/12.5)  # 每一秒取5次
        self.sample_N=math.ceil(self.SamplePoint1 *1.005)
        self.sample_byte = int(self.sample_N * 16) 
        self.SamplePoint2=self.SamplePoint1//200  #每200筆資料1筆特殊
        self.Append_index=0  #塞正常資料的index

        # self.MS_BendingX = np.zeros(self.SamplePoint1)
        # self.MS_BendingY = np.zeros(self.SamplePoint1)
        # self.MS_BendingXY = np.zeros(self.SamplePoint1)
        # self.MS_Tension = np.zeros(self.SamplePoint1)
        # self.MS_Torsion = np.zeros(self.SamplePoint1)

        self.MS_BendingX = np.zeros(self.SamplePoint1, dtype=np.float64)
        self.MS_BendingY = np.zeros(self.SamplePoint1, dtype=np.float64)
        self.MS_BendingXY = np.zeros(self.SamplePoint1, dtype=np.float64)
        self.MS_Tension = np.zeros(self.SamplePoint1, dtype=np.float64)
        self.MS_Torsion = np.zeros(self.SamplePoint1, dtype=np.float64)

        self.MS_ADXL_X = np.zeros(self.SamplePoint2, dtype=np.float64)
        self.MS_ADXL_Y = np.zeros(self.SamplePoint2, dtype=np.float64)
        self.MS_ADXL_Z = np.zeros(self.SamplePoint2, dtype=np.float64)

         #影響刻度值，顯示比例
        self.XY_3Dconstant = 0.13604
        self.Gxy_3Dconstant = 100 #2500/25
        self.Gz_3Dconstant = 20 #500/25

        # 添加可變更的 3D 散點
        # self.scatter = None
        # self.add_random_points()
        #self.add_cube_points() # 測試驗證值

        # 設定 QTimer，每 1 秒刷新一次數據
        # self.timer = QTimer(self)
        # self.timer.timeout.connect(self.update_random_points)
        # self.timer.start(1000)  # 1000 毫秒 (1 秒)
        

    # def initializeGL(self):
    #     """ 初始化 OpenGL 設定 """
    #     glEnable(GL_MULTISAMPLE)  # 啟用多重採樣抗鋸齒
    #     glEnable(GL_BLEND)
    #     glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA)

    def add_targets(self):
        """ 在 3D 視圖中加入具有漸層色彩的靶心 """
        if not self.show_target:
            return  # 若未啟動靶心，則不繪製
        
        num_segments = 100  # 每個圓環的細分數
        base_radii = [0, 10, 15, 20]  # 基準半徑
        radii = [r * self.target_scale for r in base_radii]  # 根據縮放比例調整半徑
        colors = [
            (0.0, 0.3, 0.0, 0.01),    # 深綠（柔和）
            (0.3, 0.3, 0.0, 0.01),    # 深黃（偏橄欖綠）
            (0.3, 0.0, 0.0, 0.01),    # 深紅（磚紅色感）
            (0, 0, 0, 0.01)     # 白色（透明）
        ]

        vertices = []
        faces = []
        vertex_colors = []

        for i in range(len(radii) - 1):
            r_inner = radii[i]
            r_outer = radii[i + 1]
            color_inner = np.array(colors[i])
            color_outer = np.array(colors[i + 1])

            for j in range(num_segments):
                theta1 = 2 * np.pi * j / num_segments
                theta2 = 2 * np.pi * (j + 1) / num_segments

                # 計算內外圈的頂點座標
                v1 = np.array([r_inner * np.cos(theta1), r_inner * np.sin(theta1), 0])
                v2 = np.array([r_outer * np.cos(theta1), r_outer * np.sin(theta1), 0])
                v3 = np.array([r_outer * np.cos(theta2), r_outer * np.sin(theta2), 0])
                v4 = np.array([r_inner * np.cos(theta2), r_inner * np.sin(theta2), 0])

                # 新增頂點
                base_idx = len(vertices)
                vertices.extend([v1, v2, v3, v4])

                # 三角形面 (v1, v2, v3) & (v1, v3, v4)
                faces.append([base_idx, base_idx + 1, base_idx + 2])
                faces.append([base_idx, base_idx + 2, base_idx + 3])

                # 內外圈顏色混合（實現漸層效果）
                vertex_colors.extend([color_inner, color_outer, color_outer, color_inner])

        vertices = np.array(vertices)
        faces = np.array(faces)
        vertex_colors = np.array(vertex_colors)

        # 建立 Mesh
        mesh = gl.GLMeshItem(vertexes=vertices, faces=faces, faceColors=vertex_colors, smooth=True, drawEdges=False)
        self.view.addItem(mesh)
        self.target_mesh = mesh  # 存儲對象以便刪除


    def remove_targets(self):
        """ 移除靶心 """
        if hasattr(self, 'target_mesh') and self.target_mesh is not None:
            self.view.removeItem(self.target_mesh)
            self.target_mesh = None  # 清除記錄

    def add_tension_torsion_ring(self, height=0, value_percent=0.5, torsion_mean=0):
        """
        添加張力扭矩環形顯示

        參數:
        height: 環形的 Z 軸高度
        value_percent: 值的百分比 (-1.0 到 1.0)
        torsion_mean: 扭矩平均值
        """
        if not self.show_ring:
            return

        # 移除舊的 ring
        self.remove_ring()

        radius = 25
        n = 360  # 圓的細分數

        # 計算角度
        angle = abs(int(180 * value_percent))
        direction = 1 if value_percent >= 0 else -1
        if angle > 180:
            angle = 180  # 限制在半圓內

        # 生成圓弧點
        circle_pts = []
        for i in range(angle + 1):
            temp_angle = 2 * math.pi * (i * direction / n)
            x = radius * math.cos(temp_angle)
            y = radius * math.sin(temp_angle)
            circle_pts.append([x, y])

        if len(circle_pts) < 2:
            return

        # 創建環形網格
        vertices, faces, colors = self._create_ring_mesh(circle_pts, height, value_percent)

        if len(vertices) > 0 and len(faces) > 0:
            # 創建 GLMeshItem
            self.ring_mesh = gl.GLMeshItem(
                vertexes=vertices,
                faces=faces,
                faceColors=colors,
                smooth=True,
                drawEdges=False
            )
            self.view.addItem(self.ring_mesh)

            # 添加環形條紋效果
            self.add_ring_bars(circle_pts, height, value_percent)

    def _create_ring_mesh(self, circle_pts, height, value_percent):
        """創建環形網格的頂點、面和顏色"""
        vertices = []
        faces = []
        colors = []

        # 環形的厚度層
        ring_layers = [
            (height, height - 0.2),      # 外層
            (height - 0.2, height - 0.3), # 中層
            (height - 0.3, height - 0.5)  # 內層
        ]

        # 為每一層創建頂點
        for layer_idx, (z_top, z_bottom) in enumerate(ring_layers):
            layer_vertices = []
            layer_colors = []

            # 頂部和底部頂點
            for pt in circle_pts:
                # 頂部頂點
                layer_vertices.append([pt[0], pt[1], z_top])
                # 底部頂點
                layer_vertices.append([pt[0], pt[1], z_bottom])

                # 根據層數設置顏色
                if layer_idx == 0:  # 外層
                    top_color = [0.0196, 0.3137, 0.8823, 0.0]
                    bottom_color = [0.3725, 1.0, 1.0, 0.8]
                elif layer_idx == 1:  # 中層
                    top_color = [0.3725, 1.0, 1.0, 0.8]
                    bottom_color = [0.3725, 1.0, 1.0, 0.8]
                else:  # 內層
                    top_color = [0.3725, 1.0, 1.0, 0.8]
                    bottom_color = [0.0196, 0.3137, 0.8823, 0.0]

                layer_colors.extend([top_color, bottom_color])

            # 添加到總列表
            base_idx = len(vertices)
            vertices.extend(layer_vertices)
            colors.extend(layer_colors)

            # 創建面（四邊形條帶）
            for i in range(len(circle_pts) - 1):
                # 每個四邊形由兩個三角形組成
                v1 = base_idx + i * 2      # 當前點頂部
                v2 = base_idx + i * 2 + 1  # 當前點底部
                v3 = base_idx + (i + 1) * 2 + 1  # 下一點底部
                v4 = base_idx + (i + 1) * 2      # 下一點頂部

                # 第一個三角形
                faces.append([v1, v2, v3])
                # 第二個三角形
                faces.append([v1, v3, v4])

        return np.array(vertices), np.array(faces), np.array(colors)

    def remove_ring(self):
        """移除環形顯示"""
        if hasattr(self, 'ring_mesh') and self.ring_mesh is not None:
            self.view.removeItem(self.ring_mesh)
            self.ring_mesh = None

        # 移除環形條紋
        if hasattr(self, 'ring_bars') and self.ring_bars:
            for bar in self.ring_bars:
                self.view.removeItem(bar)
            self.ring_bars = []

    def set_ring_visibility(self, visible):
        """設置環形顯示的可見性"""
        self.show_ring = visible
        if not visible:
            self.remove_ring()

    def add_ring_bars(self, circle_pts, height, value_percent):
        """添加環形條紋效果"""
        if not circle_pts or len(circle_pts) < 2:
            return

        # 判斷正負與起始顏色、每刻度漸變顏色
        e1_r, e1_g, e1_b = 0.353, 0.5294, 0.7647
        e2_r, e2_g, e2_b = 0.098, 0.1568, 0.2549

        if value_percent > 0:
            s1_r, s1_g, s1_b = 0.2353, 0.8627, 0.7056
            s2_r, s2_g, s2_b = 0.0588, 0.3137, 0.2353
        else:
            s1_r, s1_g, s1_b = 0.4705, 0.196, 0.8235
            s2_r, s2_g, s2_b = 0.1372, 0.0, 0.2745

        # 計算顏色漸變
        t1_r = (e1_r - s1_r) / (35 / 0.4)
        t1_g = (e1_g - s1_g) / (35 / 0.4)
        t1_b = (e1_b - s1_b) / (35 / 0.4)
        t2_r = (e2_r - s2_r) / (35 / 0.4)
        t2_g = (e2_g - s2_g) / (35 / 0.4)
        t2_b = (e2_b - s2_b) / (35 / 0.4)

        # 創建線條
        lines = []
        colors = []
        temp_tick = 1

        plot_n = len(circle_pts)
        if plot_n > 180:
            plot_n = 181

        for i in range(0, plot_n, 2):  # 調整bar數量密度
            if i < len(circle_pts):
                pt = circle_pts[i]

                # 起始點和結束點
                start_point = [pt[0], pt[1], height]
                end_point = [pt[0], pt[1], height + i / 10]

                lines.extend([start_point, end_point])

                # 起始顏色和結束顏色
                start_color = [
                    s1_r + t1_r * temp_tick,
                    s1_g + t1_g * temp_tick,
                    s1_b + t1_b * temp_tick,
                    0.2
                ]
                end_color = [
                    s2_r + t2_r * temp_tick,
                    s2_g + t2_g * temp_tick,
                    s2_b + t2_b * temp_tick,
                    0.8
                ]

                colors.extend([start_color, end_color])
                temp_tick += 1

        if lines:
            # 創建線條物件
            lines_array = np.array(lines)
            colors_array = np.array(colors)

            line_item = gl.GLLinePlotItem(
                pos=lines_array,
                color=colors_array,
                width=5,
                antialias=True,
                mode='lines'
            )

            # 將線條添加到場景中
            self.view.addItem(line_item)

            # 保存線條物件以便後續移除
            if not hasattr(self, 'ring_bars'):
                self.ring_bars = []
            self.ring_bars.append(line_item)


    def update_scene(self, axis_length, grid_size, grid_spacing, tick_spacing):
        # 先清除舊有的場景元素（如果存在）
        if hasattr(self, 'grid') and self.grid and self.grid in self.view.items:
            self.view.removeItem(self.grid)
            self.grid_item = None
        if hasattr(self, 'axis_items'):
            for axis in self.axis_items:
                if axis in self.view.items:
                    self.view.removeItem(axis)
            self.axis_items = []
        if hasattr(self, 'label_items'):
            for label in self.label_items:
                if label in self.view.items:
                    self.view.removeItem(label)
            self.label_items = []

        
        # 更新靶心縮放比例
        self.target_scale = axis_length / 20.0  # 根據軸長度調整靶心大小
        
        self.update_grid(grid_size, grid_spacing)  # 先更新網格
        self.update_axis(axis_length)  # 讓軸線與網格對齊
        self.update_labels(tick_spacing, axis_length)  # 確保標籤與軸對齊
        
        # 如果靶心正在顯示，重新繪製
        if self.show_target:
            self.remove_targets()
            self.add_targets()

    def update_targets(self, show_target):
        self.show_target = show_target
        if self.show_target:
            self.add_targets()
        else:
            self.remove_targets()  # 加入一個方法來清除靶心
        

    def add_axis(self):
        """ 將 XYZ 軸線移到邊緣 """
        axis_length = 30  # 軸的長度
        offset = axis_length * 1.2  # 軸的偏移量，讓它在邊邊

        # X 軸 (沿著 X 方向，貼近 Y 軸最大值)
        x_axis = np.array([[-axis_length, offset, 0], [axis_length, offset, 0]])

        # Y 軸 (沿著 Y 方向，貼近 X 軸最大值)
        y_axis = np.array([[offset, -axis_length, 0], [offset, axis_length, 0]])

        # Z 軸 (沿著 Z 方向，貼近 XY 平面邊界)
        # z_axis = np.array([[offset, 0, -axis_length], [offset, 0, axis_length]])
        z_axis = np.array([[offset, offset, -axis_length], [offset, offset, axis_length]])

        x_line = gl.GLLinePlotItem(pos=x_axis, color=(1, 0, 0, 1), width=2)
        y_line = gl.GLLinePlotItem(pos=y_axis, color=(0, 1, 0, 1), width=2)
        z_line = gl.GLLinePlotItem(pos=z_axis, color=(0, 0, 1, 1), width=2)

        self.view.addItem(x_line)
        self.view.addItem(y_line)
        self.view.addItem(z_line)
        

    def add_grid(self):
        """ 調整 XYZ 平面網格的間距，適應小數據 """
        grid_size = 70  # 原本是 200，縮小 20 倍
        grid_spacing = 1  # 原本是 20，縮小 100 倍，讓網格更密集

        # 正常網格
        grid_z = gl.GLGridItem()
        grid_z.setSize(grid_size, grid_size)
        grid_z.setSpacing(grid_spacing, grid_spacing)
        self.view.addItem(grid_z)

        # 加粗 X=0 的線 (紅色)
        x_axis = np.array([[0, -grid_size, 0], [0, grid_size, 0]])
        x_line = gl.GLLinePlotItem(pos=x_axis, color=(0.5, 0.5, 0.5, 0.5), width=3)
        self.view.addItem(x_line)

        # 加粗 Y=0 的線 (綠色)
        y_axis = np.array([[-grid_size, 0, 0], [grid_size, 0, 0]])
        y_line = gl.GLLinePlotItem(pos=y_axis, color=(0.5, 0.5, 0.5, 0.5), width=3)
        self.view.addItem(y_line)


    def update_axis(self, length):
        """更新 XYZ 軸長度，使其貼齊網格邊界"""

        # 先移除舊的軸線
        if hasattr(self, 'axis_lines'):
            for line in self.axis_lines:
                if line in self.view.items:
                    self.view.removeItem(line)
        self.axis_lines = []

        grid_size = length

        # X 軸（紅）
        x_axis = np.array([[-grid_size, grid_size, 0], [grid_size, grid_size, 0]])
        x_line = gl.GLLinePlotItem(pos=x_axis, color=(1, 0, 0, 0.8), width=2)
        
        # Y 軸（綠）
        y_axis = np.array([[grid_size, -grid_size, 0], [grid_size, grid_size, 0]])
        y_line = gl.GLLinePlotItem(pos=y_axis, color=(0, 1, 0, 0.8), width=2)

        # Z 軸（藍）
        z_axis = np.array([[grid_size, grid_size, -grid_size], [grid_size, grid_size, grid_size]])
        z_line = gl.GLLinePlotItem(pos=z_axis, color=(0, 0, 1, 0.8), width=2)

        # 灰色透明 X 軸
        x_trans = np.array([[0, -length, 0], [0, length, 0]])
        x_line_trans = gl.GLLinePlotItem(pos=x_trans, color=(0.5, 0.5, 0.5, 0.8), width=3)

        # 灰色透明 Y 軸
        y_trans = np.array([[-length, 0, 0], [length, 0, 0]])
        y_line_trans = gl.GLLinePlotItem(pos=y_trans, color=(0.5, 0.5, 0.5, 0.8), width=3)

        # 統一加入
        self.axis_lines = [x_line, y_line, z_line, x_line_trans, y_line_trans]
        for line in self.axis_lines:
            self.view.addItem(line)


    def update_grid(self, size, spacing):
        """ 更新網格大小與間距 """
        # 若已有舊網格，移除它
        if hasattr(self, 'grid_item') and self.grid_item in self.view.items:
            self.view.removeItem(self.grid_item)

        # 建立新網格
        self.grid_item = gl.GLGridItem()
        self.grid_item.setSize(size, size)
        self.grid_item.setSpacing(spacing, spacing)
        self.view.addItem(self.grid_item)


    def add_labels(self):
        """ 在 XYZ 軸上添加數字刻度 (讓數字靠邊) """
        tick_spacing = 5  # 刻度間距
        axis_range = 30  # 軸的範圍
        offset = axis_range * 1.2  # 數字也往邊邊貼

        for i in np.arange(-axis_range, axis_range + tick_spacing, tick_spacing):
            if i != 0:
                self.view.addItem(GLTextItem(f"{i}", QVector3D(i, offset, 0), color=(1, 0, 0, 1)))  # X 軸數字
                self.view.addItem(GLTextItem(f"{i}", QVector3D(offset, i, 0), color=(0, 1, 0, 1)))  # Y 軸數字
                self.view.addItem(GLTextItem(f"{i}", QVector3D(offset, offset, i), color=(0, 0, 1, 1)))  # 調整 Z 軸數字

    def update_labels(self, tick_spacing, axis_range):
        """更新 XYZ 軸數字刻度，使其與軸線對齊"""

        # 保存當前參數以便後續刷新
        self.current_tick_spacing = tick_spacing
        self.current_axis_range = axis_range

        # 清除舊的標籤（只清除我們之前加的）
        if hasattr(self, 'label_items'):
            for label in self.label_items:
                if label in self.view.items:
                    self.view.removeItem(label)
        self.label_items = []

        grid_size = axis_range
        offset = axis_range + 0.2  # 數字也往邊邊貼

        # 添加刻度數字
        for i in np.arange(-grid_size, grid_size + tick_spacing, tick_spacing):
            if i != 0:
                x_label = GLTextItem(f"{i}", QVector3D(i - 0.1, offset, 0),
                                   color=(1, 0, 0, 1), size=self.tick_label_size)
                y_label = GLTextItem(f"{i}", QVector3D(offset, i - 0.1, 0),
                                   color=(0, 1, 0, 1), size=self.tick_label_size)
                z_label = GLTextItem(f"{i}", QVector3D(offset, offset, i - 0.1),
                                   color=(0, 0, 1, 1), size=self.tick_label_size)

                self.view.addItem(x_label)
                self.view.addItem(y_label)
                self.view.addItem(z_label)

                # 加入追蹤清單
                self.label_items.extend([x_label, y_label, z_label])
        # 添加軸名稱（如果啟用）
        if self.show_axis_labels:
            axis_name_offset = axis_range + 2.0  # 軸名稱距離軸線更遠一些

            # X 軸名稱（紅色）- 顯示在 X 軸中間
            x_name = GLTextItem("X", QVector3D(0, axis_name_offset, 0),
                              color=(1, 0, 0, 1), size=self.axis_label_size)

            # Y 軸名稱（綠色）- 顯示在 Y 軸中間
            y_name = GLTextItem("Y", QVector3D(axis_name_offset, 0, 0),
                              color=(0, 1, 0, 1), size=self.axis_label_size)

           # Z 軸名稱（藍色）- 顯示在 Z 軸中間
            z_name = GLTextItem("Z", QVector3D(axis_name_offset, axis_name_offset, 0),
                              color=(0, 0, 1, 1), size=self.axis_label_size)

            self.view.addItem(x_name)
            self.view.addItem(y_name)
            self.view.addItem(z_name)

            # 加入追蹤清單
            self.label_items.extend([x_name, y_name, z_name])

    def set_tick_label_size(self, size):
        """設置刻度數字的大小"""
        self.tick_label_size = size
        # 如果當前有顯示的標籤，重新更新它們
        if hasattr(self, 'label_items') and self.label_items:
            # 重新繪製標籤以應用新的大小
            self.refresh_labels()

    def set_axis_label_size(self, size):
        """設置軸名稱的大小"""
        self.axis_label_size = size
        # 如果當前有顯示的標籤，重新更新它們
        if hasattr(self, 'label_items') and self.label_items:
            # 重新繪製標籤以應用新的大小
            self.refresh_labels()

    def set_axis_labels_visibility(self, visible):
        """設置軸名稱的可見性"""
        self.show_axis_labels = visible
        # 重新繪製標籤以應用新的設置
        if hasattr(self, 'label_items'):
            self.refresh_labels()

    def refresh_labels(self):
        """重新繪製所有標籤"""
        # 獲取當前的軸範圍和刻度間距
        if hasattr(self, 'current_axis_range') and hasattr(self, 'current_tick_spacing'):
            self.update_labels(self.current_tick_spacing, self.current_axis_range)
        else:
            # 使用默認值
            self.update_labels(2, 10)

    def set_label_settings(self, tick_size=None, axis_size=None, show_axis_names=None):
        """一次性設置所有標籤相關的設置"""
        if tick_size is not None:
            self.tick_label_size = tick_size
        if axis_size is not None:
            self.axis_label_size = axis_size
        if show_axis_names is not None:
            self.show_axis_labels = show_axis_names

        # 重新繪製標籤
        self.refresh_labels()

    def update_CFdata(self, data):
        self.CF_maximum = data  # 最大值

    def toggle_ring_display(self, show=None):
        """切換或設置 Ring 顯示狀態"""
        if show is None:
            self.show_ring = not self.show_ring
        else:
            self.show_ring = show

        if not self.show_ring:
            self.remove_ring()

    def update_ring_with_tension(self, tension_value, height=0):
        """使用張力值更新 Ring 顯示"""
        if not self.show_ring:
            return

        value_percent = tension_value / self.CF_maximum if self.CF_maximum != 0 else 0
        value_percent = max(-1.0, min(1.0, value_percent))

        torsion_mean = 0  # 可以根據需要傳入實際的扭矩值
        self.add_tension_torsion_ring(height, value_percent, torsion_mean)

    def update_points(self, data, plot_type):
        try:
            # 清除現有圖形
            if self.scatter and self.scatter in self.view.items:
                self.view.removeItem(self.scatter)
                self.scatter = None
            if self.line and self.line in self.view.items:
                self.view.removeItem(self.line)
                self.line = None
            if self.scatter_adxl and self.scatter_adxl in self.view.items:
                self.view.removeItem(self.scatter_adxl)
                self.scatter_adxl = None

            # 解包數據
            (self.MS_BendingX, self.MS_BendingY, self.MS_BendingXY, self.MS_Tension, self.MS_Torsion,
            self.MS_Temperature, self.MS_ADXL_X, self.MS_ADXL_Y, self.MS_ADXL_Z,
            temp_RSSI, Charging_Flag_temp, sleep_mode_temp, temp_battery, co2_g) = data

            min_length = min(len(self.MS_BendingX), len(self.MS_BendingY), len(self.MS_BendingXY))
            if min_length < self.SamplePoint1:
                print("Warning: Data length is insufficient for processing")
                return

            # 根據 plot_type 設定場景參數
            if plot_type == 'scatter' or plot_type == 'line':
                self.update_scene(axis_length=self.CF_maximum, grid_size=self.CF_maximum*2, grid_spacing=self.CF_maximum/10, tick_spacing=self.CF_maximum/10*2)  # Updated to use fixed values
            if plot_type == 'ADXL':
                self.update_scene(axis_length=2000, grid_size=4000, grid_spacing=500, tick_spacing=250)

            # 繪製對應圖形
            if plot_type == 'scatter':
                positions = np.column_stack([
                    self.MS_BendingX[::2],
                    self.MS_BendingY[::2],
                    np.arange(1, len(self.MS_BendingX[::2]) + 1) / 25
                ])
                if not self.scatter:
                    colors = np.zeros((len(positions), 4))
                    colors[:, 1] = 1.0  # 綠色
                    colors[:, 3] = 1.0  # 不透明
                    self.scatter = gl.GLScatterPlotItem(pos=positions, color=colors, size=1)
                    self.view.addItem(self.scatter)
                else:
                    self.scatter.setData(pos=positions)

            elif plot_type == 'line':
                x_data = self.MS_BendingX[::2]
                y_data = self.MS_BendingY[::2]
                z_data = np.arange(1, len(x_data) + 1) / 25
                lines = np.column_stack([x_data, y_data, z_data])
                if not self.line:
                    self.line = gl.GLLinePlotItem(pos=lines, color=(0, 1, 0, 1), width=2)
                    self.view.addItem(self.line)
                else:
                    self.line.setData(pos=lines)

            elif plot_type == 'ADXL':
                pp = np.array([
                    [self.MS_ADXL_X[i] / self.Gxy_3Dconstant,
                    self.MS_ADXL_Y[i] / self.Gxy_3Dconstant,
                    self.MS_ADXL_Z[i] / self.Gz_3Dconstant]
                    for i in range(self.SamplePoint2)
                ], dtype=np.float64)
                if not self.scatter_adxl:
                    colors_adxl = np.ones((len(pp), 3)) * [1, 1, 0]  # 黃色
                    self.scatter_adxl = gl.GLScatterPlotItem(pos=pp, color=colors_adxl, size=5)
                    self.view.addItem(self.scatter_adxl)
                else:
                    self.scatter_adxl.setData(pos=pp)

            # 更新 Ring 顯示（如果啟用）
            if self.show_ring and hasattr(self, 'MS_Tension') and len(self.MS_Tension) > 0:
                # 使用最新的張力數據計算 Ring
                latest_tension = self.MS_Tension[-1] if len(self.MS_Tension) > 0 else 0
                value_percent = latest_tension / self.CF_maximum if self.CF_maximum != 0 else 0

                # 限制在 -1 到 1 之間
                value_percent = max(-1.0, min(1.0, value_percent))

                # 計算扭矩平均值（如果有扭矩數據）
                torsion_mean = 0
                if hasattr(self, 'MS_Torsion') and len(self.MS_Torsion) > 0:
                    # torsion_mean = np.mean(self.MS_Torsion[-10:])  # 使用最近10個點的平均值
                    torsion_mean = np.max(self.MS_Torsion[-10:])  # 使用最近10個點的最大值

                # 更新 Ring 顯示
                ring_height = 0  # 可以根據需要調整高度
                self.add_tension_torsion_ring(ring_height, value_percent, torsion_mean)

        except Exception as e:
            print(f"Error in update_points: {e}")

    def resizeEvent(self, event):
        """ 視窗大小變更時調整 OpenGL Widget """
        super().resizeEvent(event)
        self.view.resize(self.width(), self.height())

    # 測試 驗證 值
    def add_cube_points(self):
        """ 生成 2x2x2 立方體的 3D 散點 """
        positions = np.array([
            [-2, -2, 0], [2, 2, 0], [0, 2, 0], [2, 2, 0],  # 底面 4 點
            [-2, -2, 2], [2, 0, 2], [0, 2, 2], [2, 2, 2]   # 頂面 4 點
        ])

        # 設定顏色（純綠色）
        colors = np.zeros((8, 4))  # 8 個點
        colors[:, 1] = 1.0  # 設定綠色通道為 1
        colors[:, 3] = 1.0  # 設定透明度為 1

        # 建立 3D 散點圖
        self.scatter = gl.GLScatterPlotItem(pos=positions, color=colors, size=5)
        self.view.addItem(self.scatter)

    # def add_random_points(self):
    #     """ 初始化隨機 3D 散點 """
    #     num_points = 50
    #     positions = np.random.uniform(-100, 100, (num_points, 3))
    #     colors = np.random.rand(num_points, 4)
    #     colors[:, 3] = 1.0  # 設定透明度

    #     self.scatter = gl.GLScatterPlotItem(pos=positions, color=colors, size=5)
    #     self.view.addItem(self.scatter)

    # def update_random_points(self):
    #     """ 更新 3D 散點數據 """
    #     if self.scatter:
    #         self.view.removeItem(self.scatter)  # 先移除舊數據

    #     num_points = 50
    #     positions = np.random.uniform(-100, 100, (num_points, 3))
    #     colors = np.random.rand(num_points, 4)
    #     colors[:, 3] = 1.0  # 設定透明度

    #     self.scatter = gl.GLScatterPlotItem(pos=positions, color=colors, size=5)
    #     self.view.addItem(self.scatter)  # 加入新數據


# if __name__ == '__main__':
#     app = QApplication(sys.argv)
#     window = Animation_3D()
#     window.resize(800, 600)
#     window.show()
#     sys.exit(app.exec_())