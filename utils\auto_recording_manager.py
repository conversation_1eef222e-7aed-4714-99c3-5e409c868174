# -*- coding: utf-8 -*-
"""
Auto Recording Manager
Handles automatic recording based on sensor thresholds
"""

import time
import numpy as np
from collections import deque
from PySide2.QtCore import QObject, Signal
from utils.text_file_manager import TextFileManager
from . import logger

class AutoRecordingManager(QObject):
    """Manages automatic recording based on sensor thresholds"""
    
    # Signals
    auto_record_triggered = Signal(str)  # Emits trigger reason
    auto_record_stopped = Signal()
    
    def __init__(self):
        super().__init__()
        
        # Auto recording settings
        self.auto_record_enabled = False
        self.auto_pre_record_seconds = 0
        self.auto_record_seconds = 10
        self.auto_max_record_count = 100
        self.sample_rate = 10000  # Default sample rate, will be updated
        
        # Trigger thresholds
        self.auto_cf_threshold = 0.0
        self.auto_fz_threshold = 0.0
        self.auto_t_threshold = 0.0
        self.auto_cf_enabled = False
        self.auto_fz_enabled = False
        self.auto_t_enabled = False
        
        # Recording state
        self.is_recording = False
        self.record_count = 0
        self.trigger_reason = ""
        
        # Data point counting for precise timing
        self.auto_pre_record_data_points = 0
        self.auto_record_data_points = 0
        self.current_data_point_count = 0
        
        # Pre-recording buffer (circular buffer for storing recent data)
        self.pre_record_buffer = None
        self.pre_record_buffer_size = 0
        
        # File management
        self.file_manager = None
        self.current_filename = ""
        self.directory = ""

        # Tool data for first line
        self.tool_info_text = ""
        
    def configure(self, settings):
        """Configure auto recording settings"""
        self.auto_record_enabled = bool(settings.get('auto_record_enabled', False))
        self.auto_pre_record_seconds = settings.get('auto_pre_record_seconds', 0)
        self.auto_record_seconds = settings.get('auto_record_seconds', 10)
        self.auto_max_record_count = settings.get('auto_max_record_count', 100)
        self.sample_rate = settings.get('sample_rate', 10000)
        
        # Calculate data points needed
        self.auto_pre_record_data_points = int(self.auto_pre_record_seconds * self.sample_rate)
        self.auto_record_data_points = int(self.auto_record_seconds * self.sample_rate)
        
        # Initialize pre-record buffer if needed
        if self.auto_pre_record_seconds > 0:
            # Using list to ensure the first line is always [*, ...]
            self.pre_record_buffer = []
            
            # Using deque
            # self.pre_record_buffer_size = self.auto_pre_record_data_points
            # self.pre_record_buffer = deque(maxlen=self.pre_record_buffer_size)
        
        # Threshold settings
        self.auto_cf_threshold = settings.get('auto_cf_threshold', 0.0)
        self.auto_fz_threshold = settings.get('auto_fz_threshold', 0.0)
        self.auto_t_threshold = settings.get('auto_t_threshold', 0.0)
        self.auto_cf_enabled = bool(settings.get('auto_cf_enabled', False))
        self.auto_fz_enabled = bool(settings.get('auto_fz_enabled', False))
        self.auto_t_enabled = bool(settings.get('auto_t_enabled', False))

        # Tool data
        self.tool_info_text = settings.get('tool_info_text', "")
        
        logger.info(f"Auto recording configured: auto_record_enabled={self.auto_record_enabled}, "
                   f"auto_pre_record={self.auto_pre_record_seconds}s ({self.auto_pre_record_data_points} points), "
                   f"auto_record={self.auto_record_seconds}s ({self.auto_record_data_points} points), "
                   f"sample_rate={self.sample_rate}")
    
    def set_file_settings(self, directory, filename):
        """Set file directory and filename for recording"""
        self.directory = directory
        self.current_filename = filename
        self.file_manager = TextFileManager(directory)
        
    def _make_filename_safe(self, text):
        """Make text safe for filenames"""
        # Replace problematic characters
        replacements = {
            ' ': '_',
            '(': '',
            ')': '',
            '>=': 'gte',
            '<=': 'lte',
            '>': 'gt',
            '<': 'lt',
            '+': 'plus',
            '/': '_',
            '\\': '_',
            ':': '_',
            '*': '_',
            '?': '_',
            '"': '_',
            '|': '_'
        }
        
        result = text
        for old, new in replacements.items():
            result = result.replace(old, new)
        
        return result
        
    def check_triggers(self, sensor_data):
        """Check if any sensor values exceed thresholds"""
        if not self.auto_record_enabled or self.is_recording:
            return False
            
        # Extract sensor values
        ms_bending_xy = sensor_data[2]  # MS_BendingXY
        ms_tension = sensor_data[3]     # MS_Tension  
        ms_torsion = sensor_data[4]     # MS_Torsion
        
        # Calculate cutting force (using BendingXY as approximation)
        cutting_force = np.max(ms_bending_xy) if len(ms_bending_xy) > 0 else 0
        
        # Check each threshold
        trigger_reasons = []
        
        if self.auto_cf_enabled and cutting_force >= self.auto_cf_threshold:
            trigger_reasons.append(f"CF({cutting_force:.2f} >= {self.auto_cf_threshold})")
            
        if self.auto_fz_enabled and np.max(ms_tension) >= self.auto_fz_threshold:
            trigger_reasons.append(f"Fz({np.max(ms_tension):.2f} >= {self.auto_fz_threshold})")
            
        if self.auto_t_enabled and np.max(ms_torsion) >= self.auto_t_threshold:
            trigger_reasons.append(f"T({np.max(ms_torsion):.2f} >= {self.auto_t_threshold})")
        
        if trigger_reasons:
            self.trigger_reason = " + ".join(trigger_reasons)
            logger.info(f"Auto recording triggered: {self.trigger_reason}")
            self._handle_trigger()
            return True
            
        return False
    
    def _handle_trigger(self):
        """Handle trigger event"""
        if self.record_count >= self.auto_max_record_count:
            logger.warning(f"Maximum record count ({self.auto_max_record_count}) reached")
            return
            
        if self.auto_pre_record_seconds > 0:
            # Check if buffer has enough data points
            if len(self.pre_record_buffer) >= self.auto_pre_record_data_points:
                self._start_recording()
            else:
                logger.info(f"Pre-recording buffer not full yet: {len(self.pre_record_buffer)}/{self.auto_pre_record_data_points}")
        else:
            # Start recording immediately
            self._start_recording()
    
    def _start_recording(self):
        """Start automatic recording"""
        if self.is_recording:
            return
            
        self.is_recording = True
        self.record_count += 1
        self.current_data_point_count = 0
        
        # Create new filename with timestamp and trigger info
        timestamp = time.strftime("%Y%m%d_%H%M%S", time.localtime())
        trigger_safe = self._make_filename_safe(self.trigger_reason)
        self.current_filename = f"AutoRecord_{timestamp}_{trigger_safe}_{self.record_count}.msra"
        
        # Create file
        if self.file_manager:
            self.file_manager.create_file(self.current_filename)
            logger.info(f"Auto recording started: {self.current_filename}")

        # Write tool info text (first line)
        self.write_data(self.tool_info_text)
        
        # Write pre-record buffer data if available
        if self.pre_record_buffer:
            logger.info(f"Writing {len(self.pre_record_buffer)} pre-recorded data points")
            text = "\n".join(self.pre_record_buffer)
            self.write_data(text)
        
        # Emit signal
        self.auto_record_triggered.emit(self.trigger_reason)
    
    def _stop_recording(self):
        """Stop automatic recording"""
        if not self.is_recording:
            return
            
        self.is_recording = False
        logger.info(f"Auto recording stopped after {self.current_data_point_count} data points")
        
        # Emit signal
        self.auto_record_stopped.emit()
    
    def process_data_point(self, data_line):
        """Process a single data point - called for each sensor data update"""
        if not self.auto_record_enabled:
            return
            
        # Always maintain pre-record buffer if configured
        if self.auto_pre_record_seconds > 0:

            # Add data lines to pre-record buffer
            data_lines = data_line.splitlines()
            self.pre_record_buffer.extend(data_lines)
            self.pre_record_buffer_size = len(self.pre_record_buffer)

            # Trim pre-record buffer to maintain approximate size, only at lines that are [*, ...]
            if self.pre_record_buffer_size >= self.auto_pre_record_data_points:
                # logger.debug(f'pre_record_buffer_size: {self.pre_record_buffer_size}')
                pop_idx = 0
                for index, line in enumerate(self.pre_record_buffer):
                    if line.startswith("*"):
                        if self.pre_record_buffer_size - index < self.auto_pre_record_data_points:
                            break
                        else: 
                            pop_idx = index
            
                # logger.debug(f'pop_idx: {pop_idx}')
                
                self.pre_record_buffer = self.pre_record_buffer[pop_idx:]
                self.pre_record_buffer_size = len(self.pre_record_buffer)

                # logger.debug(f'pre_record_buffer: {self.pre_record_buffer[0]}')
                # logger.debug(f'pre_record_buffer_size: {self.pre_record_buffer_size}')

            # logger.debug(f'pre_record_buffer_size: {self.pre_record_buffer_size}')
        
        # Check if we're recording
        if self.is_recording:
            self.current_data_point_count += data_line.count("\n")
            self.write_data(data_line)
            
            # logger.info(f"Auto recording: {self.current_data_point_count} data points")
            # Check if we've reached the target number of data points
            if self.current_data_point_count >= self.auto_record_data_points:
                logger.info(f"Auto recording stopped after {self.current_data_point_count} data points")
                self._stop_recording()
    
    def write_data(self, data_line):
        """Write data line to current recording file"""
        if self.is_recording and self.file_manager and self.current_filename:
            self.file_manager.add_text(self.current_filename, data_line)
    
    def reset(self):
        """Reset auto recording state"""
        self.is_recording = False

        self.trigger_reason = ""
        self.current_data_point_count = 0
        if self.pre_record_buffer:
            self.pre_record_buffer.clear()
            self.pre_record_buffer_size = 0

        logger.info("Auto recording reset")
        
    def get_status(self):
        """Get current status"""
        return {
            'auto_record_enabled': self.auto_record_enabled,
            'is_recording': self.is_recording,
            'auto_pre_recording_enabled': self.auto_pre_record_seconds > 0,
            'record_count': self.record_count,
            'auto_max_record_count': self.auto_max_record_count,
            'current_data_points': self.current_data_point_count,
            'target_data_points': self.auto_record_data_points,
            'trigger_reason': self.trigger_reason,
            'pre_record_buffer_size': len(self.pre_record_buffer)
        } 