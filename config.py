import os
from dotenv import load_dotenv

# 載入 .env 檔案
load_dotenv()

data_queue = None
scanToolIP_thread=False # 這是要給toollist查看連線
currentIP = None
MS_BendingXY = None

# 網路設備連線設定
NETWORK_DEVICE_URL = os.getenv('NETWORK_DEVICE_URL')
NETWORK_DEVICE_USERNAME = os.getenv('NETWORK_DEVICE_USERNAME')
NETWORK_DEVICE_PASSWORD = os.getenv('NETWORK_DEVICE_PASSWORD')

# 檢查必要的環境變數是否存在
if not all([NETWORK_DEVICE_URL, NETWORK_DEVICE_USERNAME, NETWORK_DEVICE_PASSWORD]):
    raise ValueError("請確認 .env 檔案中已設定 NETWORK_DEVICE_URL、NETWORK_DEVICE_USERNAME 和 NETWORK_DEVICE_PASSWORD")



# 系統預設值
DEFAULT_DISPLAY_DECIMALS = 3

IN_WINDOW_CO2_DISPLAY_DECIMALS = 3  # 主畫面 CO2 顯示專用
CO2_SETTING_DISPLAY_DECIMALS = 3    # CO2 設定項目專用

DEFAULT_CO2_DATA = {
    'MS_CO2_K': 5,
    'MS_CO2_zc': 4, 
    'MS_CO2_Dc': 6.0, 
    'MS_CO2_vc': 100.0,
    'MS_CO2_fz': 0.05, 
    'MS_CO2_ap': 1.0, 
    'MS_CO2_ae': 3.0, 
    'MS_CO2_n': 5305.2,
    'MS_CO2_vf': 1061.04, 
    'MS_CO2_Q': 3.183, 
    'MS_CO2_Pc': 1.0, 'MS_CO2_Pb': 0.275, # unit: kW  
    #估算：主軸額定最大功率5.5kW，空轉估額定最大功率之5%；輕加工主軸功率約1kW以下
    'MS_CO2_EF': 0.474 , # unit: kgCO2/kWh
    'init_status' : 0
}

CO2_K_VALUES = {
    "Alloy Steel": 5,
    "Titanium Alloy": 7,
    "Structural Steel": 10,
    "Cast Steel": 15,
    "Cast Iron": 30,
    "Aluminum Alloy": 60,
    "Custom Value": None
}

CO2_EF_VALUES = {
    "0.474": 0.474,  # for Taiwan #113年度台電公告值(2025/4/14公告)
    "0.510": 0.510,  # for China
    "Custom Value": None
}

DEFAULT_FILTER = {
    "filter_type": "Nofilter_radio",
    "filter_values": 1,
    "Lowfilter_edit": 999,
    "Highfilter_edit": 1,
    "SGfilter_edit": 4,
    "MAfilter_edit": 1
}